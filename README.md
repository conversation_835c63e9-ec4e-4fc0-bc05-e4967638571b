# TikTok Live Monitor

一个基于WebSocket的TikTok直播间数据监听系统，支持服务端控制和多房间管理。

## 功能特性

- 🚀 **WebSocket通信**: 服务端和客户端之间的实时双向通信
- 🏠 **多房间管理**: 支持同时监听多个直播间
- 🎯 **服务端控制**: 通过服务端动态添加/删除监听房间
- 📊 **事件上报**: 实时上报直播间事件（点赞、评论、礼物等）
- 🔧 **自定义协议**: 定义了完整的消息协议和事件类型
- 🖥️ **管理界面**: 提供Web管理界面进行房间管理
- ⚙️ **灵活配置**: 支持配置文件和环境变量配置

## 系统架构

```
┌─────────────────┐    WebSocket    ┌─────────────────┐
│   服务端        │ ←──────────────→ │   客户端        │
│                 │                 │                 │
│ - HTTP API      │                 │ - Electron App  │
│ - WebSocket服务 │                 │ - 房间管理器    │
│ - 房间管理      │                 │ - 事件监听      │
│ - 事件分发      │                 │ - 数据上报      │
└─────────────────┘                 └─────────────────┘
         │                                   │
         │                                   │
    ┌────▼────┐                         ┌────▼────┐
    │ Web管理 │                         │ 浏览器   │
    │ 界面    │                         │ 页面    │
    └─────────┘                         └─────────┘
```

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动服务端

```bash
# 使用默认配置启动
npm run start:server

# 或者使用开发模式
npm run dev:server
```

服务端将启动：
- HTTP API服务器: http://localhost:3000
- WebSocket服务器: ws://localhost:8080
- 管理界面: http://localhost:3000/admin.html

### 3. 启动客户端

```bash
# 使用默认配置启动
npm run start:client

# 或者使用开发模式
npm run dev:client
```

### 4. 添加监听房间

通过HTTP API添加房间：

```bash
curl -X POST http://localhost:3000/api/rooms \
  -H "Content-Type: application/json" \
  -d '{
    "roomId": "test_room_001",
    "roomUrl": "@username"
  }'
```

或者通过Web管理界面：http://localhost:3000/admin.html

## API文档

### HTTP API

#### 获取服务器状态
```
GET /api/status
```

#### 获取房间列表
```
GET /api/rooms
```

#### 添加房间监听
```
POST /api/rooms
Content-Type: application/json

{
  "roomId": "room_001",
  "roomUrl": "@username",
  "config": {
    "autoReconnect": true,
    "maxRetries": 3
  }
}
```

#### 删除房间监听
```
DELETE /api/rooms/{roomId}
```

#### 获取客户端列表
```
GET /api/clients
```

### WebSocket协议

#### 消息类型

**服务端 → 客户端**
- `room_add`: 添加房间监听
- `room_remove`: 移除房间监听
- `room_list`: 获取房间列表
- `client_status`: 获取客户端状态

**客户端 → 服务端**
- `client_ready`: 客户端就绪
- `room_status`: 房间状态更新
- `live_event`: 直播间事件
- `error`: 错误消息
- `heartbeat`: 心跳消息

#### 事件类型

- `like`: 点赞事件
- `comment`: 评论事件
- `gift`: 礼物事件
- `follow`: 关注事件
- `member_join`: 用户进房
- `fanclub_join`: 加入粉丝团
- `live_start`: 开播
- `live_end`: 下播

## 配置说明

### 配置文件

配置文件位于 `config/` 目录：

- `default.json`: 默认配置
- `development.json`: 开发环境配置
- `production.json`: 生产环境配置

### 环境变量

可以通过环境变量覆盖配置：

```bash
# 服务器配置
HTTP_HOST=0.0.0.0
HTTP_PORT=3000
WS_HOST=0.0.0.0
WS_PORT=8080

# 客户端配置
SERVER_URL=ws://localhost:8080
CLIENT_ID=my_client
MAX_ROOMS=20
PROXY_SERVER=http://127.0.0.1:8080

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log
```

### 配置项说明

```json
{
  "server": {
    "http": {
      "host": "localhost",    // HTTP服务器地址
      "port": 3000           // HTTP服务器端口
    },
    "websocket": {
      "host": "localhost",    // WebSocket服务器地址
      "port": 8080           // WebSocket服务器端口
    },
    "heartbeatInterval": 30000 // 心跳间隔(毫秒)
  },
  "client": {
    "serverUrl": "ws://localhost:8080", // 服务器WebSocket地址
    "clientId": null,                   // 客户端ID(null为自动生成)
    "maxRooms": 10,                     // 最大房间数
    "proxyServer": "http://127.0.0.1:8080", // 代理服务器
    "reconnectInterval": 5000,          // 重连间隔(毫秒)
    "maxReconnectAttempts": 10,         // 最大重连次数
    "heartbeatInterval": 30000,         // 心跳间隔(毫秒)
    "windowOptions": {                  // Electron窗口选项
      "width": 1024,
      "height": 768,
      "show": false
    }
  }
}
```

## 开发说明

### 项目结构

```
├── client/                 # 客户端代码
│   ├── client-app.js      # 客户端应用主程序
│   ├── room-manager.js    # 房间管理器
│   └── websocket-client.js # WebSocket客户端
├── server/                 # 服务端代码
│   ├── server-manager.js  # 服务端管理器
│   └── websocket-server.js # WebSocket服务器
├── protocol/               # 协议定义
│   └── websocket-protocol.js # WebSocket协议
├── config/                 # 配置文件
│   ├── default.json       # 默认配置
│   └── config-loader.js   # 配置加载器
├── public/                 # 静态文件
│   └── admin.html         # 管理界面
├── src/                    # 原有TikTok监听代码
├── start-server.js         # 服务端启动脚本
├── start-client.js         # 客户端启动脚本
└── README.md              # 说明文档
```

### 扩展开发

1. **添加新的事件类型**: 在 `protocol/websocket-protocol.js` 中定义
2. **自定义事件处理**: 在 `server/server-manager.js` 的 `handleLiveEvent` 方法中添加
3. **添加新的API**: 在 `server/server-manager.js` 的 `setupRoutes` 方法中添加
4. **自定义客户端逻辑**: 在 `client/client-app.js` 中扩展

## 注意事项

1. **代理设置**: TikTok可能需要代理访问，请确保代理服务器正常运行
2. **资源限制**: 每个房间会创建一个Electron窗口，请根据系统资源调整最大房间数
3. **网络稳定性**: 确保网络连接稳定，系统会自动重连但可能丢失部分事件
4. **权限问题**: 某些系统可能需要额外权限来运行Electron应用

## 故障排除

### 常见问题

1. **连接失败**: 检查服务端是否启动，端口是否被占用
2. **房间监听失败**: 检查代理设置，确认直播间URL正确
3. **事件丢失**: 检查网络连接，查看日志输出
4. **内存占用高**: 减少最大房间数，定期重启客户端

### 日志查看

- 服务端日志: 控制台输出
- 客户端日志: 控制台输出
- Web管理界面: 实时事件日志

## 许可证

MIT License
