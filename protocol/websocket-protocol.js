/**
 * WebSocket 通信协议定义
 * 客户端和服务端之间的消息格式和事件类型定义
 */

// 消息类型枚举
const MessageType = {
  // 服务端 -> 客户端
  ROOM_ADD: 'room_add',           // 添加监听房间
  ROOM_REMOVE: 'room_remove',     // 移除监听房间
  ROOM_LIST: 'room_list',         // 获取房间列表
  CLIENT_STATUS: 'client_status', // 获取客户端状态
  
  // 客户端 -> 服务端
  ROOM_STATUS: 'room_status',     // 房间状态更新
  LIVE_EVENT: 'live_event',       // 直播间事件
  CLIENT_READY: 'client_ready',   // 客户端就绪
  ERROR: 'error',                 // 错误消息
  HEARTBEAT: 'heartbeat',         // 心跳消息
};

// 直播间事件类型
const LiveEventType = {
  LIKE: 'like',                   // 点赞事件
  COMMENT: 'comment',             // 评论事件
  GIFT: 'gift',                   // 礼物事件
  FOLLOW: 'follow',               // 关注事件
  MEMBER_JOIN: 'member_join',     // 用户进房
  MEMBER_LEAVE: 'member_leave',   // 用户离房
  FANCLUB_JOIN: 'fanclub_join',   // 加入粉丝团
  FANCLUB_UPGRADE: 'fanclub_upgrade', // 粉丝团升级
  LIVE_START: 'live_start',       // 开播
  LIVE_END: 'live_end',           // 下播
  LIVE_PAUSE: 'live_pause',       // 暂停
  LIVE_RESUME: 'live_resume',     // 恢复
};

// 房间状态枚举
const RoomStatus = {
  UNKNOWN: 'unknown',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  MONITORING: 'monitoring',
  ERROR: 'error',
  DISCONNECTED: 'disconnected',
  LIVE: 'live',
  OFFLINE: 'offline',
  NOT_FOUND: 'not_found',
};

// 消息基础结构
class BaseMessage {
  constructor(type, data = {}, messageId = null) {
    this.type = type;
    this.data = data;
    this.timestamp = Date.now();
    this.messageId = messageId || this.generateMessageId();
  }

  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  toJSON() {
    return {
      type: this.type,
      data: this.data,
      timestamp: this.timestamp,
      messageId: this.messageId,
    };
  }

  static fromJSON(json) {
    const obj = typeof json === 'string' ? JSON.parse(json) : json;
    return new BaseMessage(obj.type, obj.data, obj.messageId);
  }
}

// 服务端消息类型
class ServerMessage extends BaseMessage {
  // 添加房间监听
  static addRoom(roomId, roomUrl, config = {}) {
    return new ServerMessage(MessageType.ROOM_ADD, {
      roomId,
      roomUrl,
      config: {
        autoReconnect: true,
        maxRetries: 3,
        retryInterval: 5000,
        ...config,
      },
    });
  }

  // 移除房间监听
  static removeRoom(roomId) {
    return new ServerMessage(MessageType.ROOM_REMOVE, { roomId });
  }

  // 获取房间列表
  static getRoomList() {
    return new ServerMessage(MessageType.ROOM_LIST, {});
  }

  // 获取客户端状态
  static getClientStatus() {
    return new ServerMessage(MessageType.CLIENT_STATUS, {});
  }
}

// 客户端消息类型
class ClientMessage extends BaseMessage {
  // 客户端就绪
  static clientReady(clientInfo = {}) {
    return new ClientMessage(MessageType.CLIENT_READY, {
      clientId: clientInfo.clientId || 'default',
      version: clientInfo.version || '1.0.0',
      capabilities: clientInfo.capabilities || ['tiktok'],
    });
  }

  // 房间状态更新
  static roomStatus(roomId, status, details = {}) {
    return new ClientMessage(MessageType.ROOM_STATUS, {
      roomId,
      status,
      details: {
        memberCount: details.memberCount || 0,
        likeTotal: details.likeTotal || 0,
        ...details,
      },
    });
  }

  // 直播间事件
  static liveEvent(roomId, eventType, eventData) {
    return new ClientMessage(MessageType.LIVE_EVENT, {
      roomId,
      eventType,
      eventData: {
        timestamp: Date.now(),
        ...eventData,
      },
    });
  }

  // 错误消息
  static error(error, roomId = null) {
    return new ClientMessage(MessageType.ERROR, {
      roomId,
      error: {
        message: error.message || error,
        code: error.code || 'UNKNOWN_ERROR',
        stack: error.stack,
      },
    });
  }

  // 心跳消息
  static heartbeat(status = {}) {
    return new ClientMessage(MessageType.HEARTBEAT, {
      status: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        ...status,
      },
    });
  }
}

// 直播间事件数据结构
class LiveEvent {
  constructor(type, user, data = {}) {
    this.type = type;
    this.user = this.formatUser(user);
    this.data = data;
    this.timestamp = Date.now();
  }

  formatUser(user) {
    if (!user) return null;
    
    return {
      userId: user.userId || user.id,
      nickname: user.nickname || user.displayName,
      displayId: user.displayId,
      avatar: user.avatar || user.avatarUrl,
      level: user.level || user.userPrivilegeLevel,
      fansclubLevel: user.fansclubLevel,
      badges: this.formatBadges(user.combineBadges || user.badges),
    };
  }

  formatBadges(badges) {
    if (!badges || !Array.isArray(badges)) return [];
    
    return badges.map(badge => ({
      type: badge.sceneType || badge.type,
      text: badge.text || badge.str || badge.displayText,
      level: badge.level,
      color: badge.backgroundColorCode || badge.color,
      icon: badge.iconUrl || badge.icon,
    }));
  }

  // 点赞事件
  static like(user, likeCount, totalLikes) {
    return new LiveEvent(LiveEventType.LIKE, user, {
      likeCount,
      totalLikes,
    });
  }

  // 评论事件
  static comment(user, content, language = 'zh') {
    return new LiveEvent(LiveEventType.COMMENT, user, {
      content,
      language,
    });
  }

  // 礼物事件
  static gift(user, giftInfo) {
    return new LiveEvent(LiveEventType.GIFT, user, {
      giftId: giftInfo.giftId,
      giftName: giftInfo.giftName,
      giftCount: giftInfo.groupCount || giftInfo.count,
      comboCount: giftInfo.comboCount,
      isCombo: giftInfo.combo,
      isComboEnd: giftInfo.repeatEnd === 1,
      totalValue: (giftInfo.groupCount || 1) * (giftInfo.comboCount || 1),
    });
  }

  // 关注事件
  static follow(user, followCount) {
    return new LiveEvent(LiveEventType.FOLLOW, user, {
      followCount,
    });
  }

  // 用户进房事件
  static memberJoin(user, memberCount) {
    return new LiveEvent(LiveEventType.MEMBER_JOIN, user, {
      memberCount,
    });
  }

  // 粉丝团事件
  static fanclubJoin(user) {
    return new LiveEvent(LiveEventType.FANCLUB_JOIN, user, {});
  }

  static fanclubUpgrade(user, newLevel) {
    return new LiveEvent(LiveEventType.FANCLUB_UPGRADE, user, {
      newLevel,
    });
  }

  // 直播状态事件
  static liveStart(roomId) {
    return new LiveEvent(LiveEventType.LIVE_START, null, { roomId });
  }

  static liveEnd(roomId) {
    return new LiveEvent(LiveEventType.LIVE_END, null, { roomId });
  }

  static livePause(roomId) {
    return new LiveEvent(LiveEventType.LIVE_PAUSE, null, { roomId });
  }

  static liveResume(roomId) {
    return new LiveEvent(LiveEventType.LIVE_RESUME, null, { roomId });
  }
}

module.exports = {
  MessageType,
  LiveEventType,
  RoomStatus,
  BaseMessage,
  ServerMessage,
  ClientMessage,
  LiveEvent,
};
