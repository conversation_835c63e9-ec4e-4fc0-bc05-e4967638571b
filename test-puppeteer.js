#!/usr/bin/env node

/**
 * Puppeteer测试脚本
 * 验证Puppeteer和TikTok监听功能
 */

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const { startMonitorPage } = require('./src/tiktok');

// 使用stealth插件
puppeteer.use(StealthPlugin());

async function testPuppeteerMonitoring() {
  console.log('🧪 测试Puppeteer TikTok监听功能...');
  
  let browser = null;
  let page = null;
  
  try {
    // 启动浏览器
    console.log('启动浏览器...');
    browser = await puppeteer.launch({
      headless: false, // 显示浏览器窗口以便调试
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--proxy-server=http://127.0.0.1:8080', // 如果需要代理
      ],
    });
    
    // 创建页面
    page = await browser.newPage();
    
    // 设置用户代理
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
    
    // 设置视口
    await page.setViewport({ width: 1920, height: 1080 });
    
    // 测试URL（请替换为实际的TikTok直播间）
    const testUrl = 'https://www.tiktok.com/@test_user/live';
    const liveId = 'test_user';
    
    console.log(`导航到: ${testUrl}`);
    
    // 导航到页面
    await page.goto(testUrl, { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    console.log('页面加载完成，等待3秒...');
    await page.waitForTimeout(3000);
    
    // 事件处理函数
    const eventCallback = (payload) => {
      console.log('📨 收到TikTok事件:', JSON.stringify(payload, null, 2));
    };
    
    const logCallback = (info) => {
      console.log('📝 日志:', info);
    };
    
    console.log('开始监听TikTok事件...');
    
    // 开始监听
    await startMonitorPage(page, liveId, eventCallback, logCallback);
    
    console.log('✅ 监听已启动，等待事件...');
    console.log('按 Ctrl+C 停止监听');
    
    // 保持运行
    await new Promise(() => {}); // 永远等待，直到手动停止
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    if (error.message.includes('net::ERR_PROXY_CONNECTION_FAILED')) {
      console.log('💡 提示: 代理连接失败，请检查代理服务器是否运行');
      console.log('   或者修改代码中的代理设置');
    }
    
    if (error.message.includes('Navigation timeout')) {
      console.log('💡 提示: 页面加载超时，可能是网络问题或需要代理');
    }
    
  } finally {
    // 清理资源
    if (page) {
      await page.close();
    }
    if (browser) {
      await browser.close();
    }
  }
}

async function testBasicPuppeteer() {
  console.log('🧪 测试基本Puppeteer功能...');
  
  let browser = null;
  
  try {
    browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    
    const page = await browser.newPage();
    await page.goto('https://example.com');
    
    const title = await page.title();
    console.log('✅ 页面标题:', title);
    
    await page.close();
    
  } catch (error) {
    console.error('❌ 基本测试失败:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

async function main() {
  const args = process.argv.slice(2);
  const testType = args[0] || 'basic';
  
  console.log('🚀 Puppeteer TikTok 监听测试');
  console.log('================================');
  
  // 设置优雅退出
  process.on('SIGINT', () => {
    console.log('\n👋 收到退出信号，正在清理...');
    process.exit(0);
  });
  
  try {
    switch (testType) {
      case 'basic':
        await testBasicPuppeteer();
        break;
        
      case 'monitor':
        await testPuppeteerMonitoring();
        break;
        
      default:
        console.log('使用方法:');
        console.log('  node test-puppeteer.js basic    # 测试基本Puppeteer功能');
        console.log('  node test-puppeteer.js monitor  # 测试TikTok监听功能');
        break;
    }
    
  } catch (error) {
    console.error('测试执行失败:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  testPuppeteerMonitoring,
  testBasicPuppeteer,
};
