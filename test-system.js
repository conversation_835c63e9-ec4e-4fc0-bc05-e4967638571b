#!/usr/bin/env node

/**
 * 系统测试脚本
 * 用于测试WebSocket通信和房间管理功能
 */

const WebSocket = require('ws');
const { ClientMessage, MessageType } = require('./protocol/websocket-protocol');

class SystemTester {
  constructor(serverUrl = 'ws://localhost:8080') {
    this.serverUrl = serverUrl;
    this.ws = null;
    this.isConnected = false;
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runTests() {
    console.log('🚀 开始系统测试...\n');
    
    try {
      await this.connectToServer();
      await this.testClientReady();
      await this.testRoomManagement();
      await this.testEventSimulation();
      
      this.printResults();
      
    } catch (error) {
      console.error('❌ 测试失败:', error);
    } finally {
      this.disconnect();
    }
  }

  /**
   * 连接到服务器
   */
  async connectToServer() {
    return new Promise((resolve, reject) => {
      console.log(`📡 连接到服务器: ${this.serverUrl}`);
      
      this.ws = new WebSocket(this.serverUrl);
      
      this.ws.on('open', () => {
        console.log('✅ 连接成功');
        this.isConnected = true;
        this.addTestResult('连接测试', true, '成功连接到WebSocket服务器');
        resolve();
      });
      
      this.ws.on('message', (data) => {
        this.handleMessage(data);
      });
      
      this.ws.on('error', (error) => {
        console.error('❌ 连接错误:', error);
        this.addTestResult('连接测试', false, error.message);
        reject(error);
      });
      
      this.ws.on('close', () => {
        console.log('🔌 连接已关闭');
        this.isConnected = false;
      });
      
      // 设置超时
      setTimeout(() => {
        if (!this.isConnected) {
          reject(new Error('连接超时'));
        }
      }, 5000);
    });
  }

  /**
   * 处理服务器消息
   */
  handleMessage(data) {
    try {
      const message = JSON.parse(data);
      console.log('📨 收到消息:', message.type, message.data);
      
      // 这里可以添加消息处理逻辑
      if (message.type === MessageType.ROOM_ADD) {
        console.log('🏠 收到添加房间请求:', message.data);
      }
      
    } catch (error) {
      console.error('❌ 消息解析错误:', error);
    }
  }

  /**
   * 测试客户端就绪消息
   */
  async testClientReady() {
    console.log('\n🧪 测试客户端就绪消息...');
    
    try {
      const message = ClientMessage.clientReady({
        clientId: 'test_client',
        version: '1.0.0',
        capabilities: ['tiktok', 'test'],
      });
      
      this.sendMessage(message);
      this.addTestResult('客户端就绪', true, '成功发送客户端就绪消息');
      
      // 等待一下让服务器处理
      await this.sleep(1000);
      
    } catch (error) {
      this.addTestResult('客户端就绪', false, error.message);
    }
  }

  /**
   * 测试房间管理
   */
  async testRoomManagement() {
    console.log('\n🏠 测试房间管理...');
    
    try {
      // 测试房间状态更新
      const statusMessage = ClientMessage.roomStatus('test_room_001', 'connecting', {
        memberCount: 100,
        likeTotal: 1000,
      });
      
      this.sendMessage(statusMessage);
      this.addTestResult('房间状态更新', true, '成功发送房间状态更新');
      
      await this.sleep(500);
      
      // 测试房间状态变更
      const monitoringMessage = ClientMessage.roomStatus('test_room_001', 'monitoring', {
        memberCount: 150,
        likeTotal: 1500,
      });
      
      this.sendMessage(monitoringMessage);
      this.addTestResult('房间状态变更', true, '成功发送房间监听状态');
      
    } catch (error) {
      this.addTestResult('房间管理', false, error.message);
    }
  }

  /**
   * 测试事件模拟
   */
  async testEventSimulation() {
    console.log('\n🎭 测试事件模拟...');
    
    try {
      const events = [
        {
          type: 'like',
          data: {
            user: {
              userId: 'user_001',
              nickname: '测试用户1',
              displayId: '12345',
            },
            likeCount: 5,
            totalLikes: 1005,
          },
        },
        {
          type: 'comment',
          data: {
            user: {
              userId: 'user_002',
              nickname: '测试用户2',
              displayId: '67890',
            },
            content: '这是一条测试评论',
            language: 'zh',
          },
        },
        {
          type: 'gift',
          data: {
            user: {
              userId: 'user_003',
              nickname: '测试用户3',
              displayId: '11111',
            },
            giftId: 'gift_001',
            giftName: '玫瑰花',
            giftCount: 1,
            comboCount: 5,
            isCombo: true,
            isComboEnd: false,
            totalValue: 5,
          },
        },
        {
          type: 'member_join',
          data: {
            user: {
              userId: 'user_004',
              nickname: '测试用户4',
              displayId: '22222',
            },
            memberCount: 200,
          },
        },
      ];

      for (const event of events) {
        const message = ClientMessage.liveEvent('test_room_001', event.type, event.data);
        this.sendMessage(message);
        
        console.log(`📤 发送${event.type}事件`);
        await this.sleep(500);
      }
      
      this.addTestResult('事件模拟', true, `成功发送${events.length}个测试事件`);
      
    } catch (error) {
      this.addTestResult('事件模拟', false, error.message);
    }
  }

  /**
   * 发送消息
   */
  sendMessage(message) {
    if (!this.isConnected || !this.ws) {
      throw new Error('未连接到服务器');
    }
    
    this.ws.send(JSON.stringify(message.toJSON()));
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, message) {
    this.testResults.push({
      testName,
      success,
      message,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 打印测试结果
   */
  printResults() {
    console.log('\n📊 测试结果汇总:');
    console.log('='.repeat(50));
    
    let passCount = 0;
    let failCount = 0;
    
    this.testResults.forEach((result, index) => {
      const status = result.success ? '✅ 通过' : '❌ 失败';
      const icon = result.success ? '✅' : '❌';
      
      console.log(`${index + 1}. ${result.testName}: ${status}`);
      console.log(`   ${result.message}`);
      
      if (result.success) {
        passCount++;
      } else {
        failCount++;
      }
    });
    
    console.log('='.repeat(50));
    console.log(`总计: ${this.testResults.length} 个测试`);
    console.log(`通过: ${passCount} 个`);
    console.log(`失败: ${failCount} 个`);
    console.log(`成功率: ${((passCount / this.testResults.length) * 100).toFixed(1)}%`);
    
    if (failCount === 0) {
      console.log('\n🎉 所有测试通过！系统运行正常。');
    } else {
      console.log('\n⚠️  部分测试失败，请检查系统配置。');
    }
  }

  /**
   * 睡眠函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// HTTP API 测试
class HTTPTester {
  constructor(baseUrl = 'http://localhost:3000') {
    this.baseUrl = baseUrl;
  }

  async testHTTPAPI() {
    console.log('\n🌐 测试HTTP API...');
    
    try {
      // 测试服务器状态
      const statusResponse = await fetch(`${this.baseUrl}/api/status`);
      const statusData = await statusResponse.json();
      
      if (statusData.success) {
        console.log('✅ 服务器状态API正常');
        console.log('   服务器信息:', statusData.data);
      } else {
        console.log('❌ 服务器状态API异常');
      }
      
      // 测试房间列表
      const roomsResponse = await fetch(`${this.baseUrl}/api/rooms`);
      const roomsData = await roomsResponse.json();
      
      if (roomsData.success) {
        console.log('✅ 房间列表API正常');
        console.log(`   当前房间数: ${roomsData.data.length}`);
      } else {
        console.log('❌ 房间列表API异常');
      }
      
    } catch (error) {
      console.log('❌ HTTP API测试失败:', error.message);
      console.log('   请确保服务端已启动');
    }
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const wsUrl = args[0] || 'ws://localhost:8080';
  const httpUrl = args[1] || 'http://localhost:3000';
  
  console.log('🔧 TikTok Live Monitor 系统测试');
  console.log(`WebSocket URL: ${wsUrl}`);
  console.log(`HTTP API URL: ${httpUrl}`);
  
  // 测试HTTP API
  const httpTester = new HTTPTester(httpUrl);
  await httpTester.testHTTPAPI();
  
  // 测试WebSocket
  const systemTester = new SystemTester(wsUrl);
  await systemTester.runTests();
  
  console.log('\n🏁 测试完成');
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { SystemTester, HTTPTester };
