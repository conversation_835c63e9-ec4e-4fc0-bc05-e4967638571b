#!/usr/bin/env node

/**
 * 服务端启动脚本
 * 启动WebSocket服务器和HTTP API服务器
 */

const { loadConfig } = require('./config/config-loader');
const ServerManager = require('./server/server-manager');

async function startServer() {
  try {
    console.log('Starting TikTok Live Monitor Server...');
    
    // 加载配置
    const environment = process.env.NODE_ENV || 'default';
    const config = loadConfig(environment);
    
    console.log('Configuration loaded:', {
      environment,
      httpPort: config.server.http.port,
      wsPort: config.server.websocket.port,
      host: config.server.http.host,
    });
    
    // 创建服务器管理器
    const serverManager = new ServerManager({
      httpPort: config.server.http.port,
      wsPort: config.server.websocket.port,
      host: config.server.http.host,
      heartbeatInterval: config.server.heartbeatInterval,
    });
    
    // 设置优雅关闭
    const gracefulShutdown = async (signal) => {
      console.log(`\nReceived ${signal}, shutting down gracefully...`);
      
      try {
        await serverManager.stop();
        console.log('Server stopped successfully');
        process.exit(0);
      } catch (error) {
        console.error('Error during shutdown:', error);
        process.exit(1);
      }
    };
    
    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
    // 监听未捕获的异常
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      gracefulShutdown('uncaughtException');
    });
    
    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      gracefulShutdown('unhandledRejection');
    });
    
    // 启动服务器
    await serverManager.start();
    
    console.log('\n=== TikTok Live Monitor Server Started ===');
    console.log(`HTTP API: http://${config.server.http.host}:${config.server.http.port}`);
    console.log(`WebSocket: ws://${config.server.websocket.host}:${config.server.websocket.port}`);
    console.log('Press Ctrl+C to stop the server');
    console.log('==========================================\n');
    
    // API使用示例
    console.log('API Usage Examples:');
    console.log(`curl http://${config.server.http.host}:${config.server.http.port}/api/status`);
    console.log(`curl http://${config.server.http.host}:${config.server.http.port}/api/rooms`);
    console.log(`curl -X POST http://${config.server.http.host}:${config.server.http.port}/api/rooms \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -d '{"roomId": "test_room", "roomUrl": "@username"}'`);
    console.log('');
    
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本，启动服务器
if (require.main === module) {
  startServer();
}

module.exports = { startServer };
