{"server": {"http": {"host": "localhost", "port": 3000}, "websocket": {"host": "localhost", "port": 8080}, "heartbeatInterval": 30000}, "client": {"serverUrl": "ws://localhost:8080", "clientId": null, "maxRooms": 10, "proxyServer": "http://127.0.0.1:8080", "reconnectInterval": 5000, "maxReconnectAttempts": 10, "heartbeatInterval": 30000, "windowOptions": {"width": 1024, "height": 768, "show": false, "webPreferences": {"nodeIntegration": false, "contextIsolation": true}}}, "logging": {"level": "info", "enableConsole": true, "enableFile": false, "logFile": "logs/app.log"}, "features": {"enableRealTimeEvents": true, "enableEventStorage": false, "enableWebhooks": false, "enableMetrics": true}}