/**
 * 配置加载器
 * 支持从文件、环境变量等多种方式加载配置
 */

const fs = require('fs');
const path = require('path');

class ConfigLoader {
  constructor() {
    this.config = {};
    this.configDir = path.join(__dirname);
    this.defaultConfigFile = path.join(this.configDir, 'default.json');
  }

  /**
   * 加载配置
   */
  load(environment = 'default') {
    try {
      // 加载默认配置
      this.loadDefaultConfig();
      
      // 加载环境特定配置
      if (environment !== 'default') {
        this.loadEnvironmentConfig(environment);
      }
      
      // 应用环境变量覆盖
      this.applyEnvironmentVariables();
      
      // 验证配置
      this.validateConfig();
      
      return this.config;
      
    } catch (error) {
      console.error('Failed to load configuration:', error);
      throw error;
    }
  }

  /**
   * 加载默认配置
   */
  loadDefaultConfig() {
    if (fs.existsSync(this.defaultConfigFile)) {
      const defaultConfig = JSON.parse(fs.readFileSync(this.defaultConfigFile, 'utf8'));
      this.config = { ...defaultConfig };
    } else {
      console.warn('Default config file not found, using built-in defaults');
      this.config = this.getBuiltInDefaults();
    }
  }

  /**
   * 加载环境特定配置
   */
  loadEnvironmentConfig(environment) {
    const envConfigFile = path.join(this.configDir, `${environment}.json`);
    
    if (fs.existsSync(envConfigFile)) {
      const envConfig = JSON.parse(fs.readFileSync(envConfigFile, 'utf8'));
      this.config = this.mergeDeep(this.config, envConfig);
    }
  }

  /**
   * 应用环境变量覆盖
   */
  applyEnvironmentVariables() {
    // 服务器配置
    if (process.env.HTTP_HOST) {
      this.config.server.http.host = process.env.HTTP_HOST;
    }
    if (process.env.HTTP_PORT) {
      this.config.server.http.port = parseInt(process.env.HTTP_PORT);
    }
    if (process.env.WS_HOST) {
      this.config.server.websocket.host = process.env.WS_HOST;
    }
    if (process.env.WS_PORT) {
      this.config.server.websocket.port = parseInt(process.env.WS_PORT);
    }

    // 客户端配置
    if (process.env.SERVER_URL) {
      this.config.client.serverUrl = process.env.SERVER_URL;
    }
    if (process.env.CLIENT_ID) {
      this.config.client.clientId = process.env.CLIENT_ID;
    }
    if (process.env.MAX_ROOMS) {
      this.config.client.maxRooms = parseInt(process.env.MAX_ROOMS);
    }
    if (process.env.PROXY_SERVER) {
      this.config.client.proxyServer = process.env.PROXY_SERVER;
    }

    // 日志配置
    if (process.env.LOG_LEVEL) {
      this.config.logging.level = process.env.LOG_LEVEL;
    }
    if (process.env.LOG_FILE) {
      this.config.logging.logFile = process.env.LOG_FILE;
    }
  }

  /**
   * 验证配置
   */
  validateConfig() {
    // 验证必需的配置项
    const requiredPaths = [
      'server.http.host',
      'server.http.port',
      'server.websocket.host',
      'server.websocket.port',
      'client.serverUrl',
      'client.maxRooms',
    ];

    for (const path of requiredPaths) {
      if (!this.getNestedValue(this.config, path)) {
        throw new Error(`Required configuration missing: ${path}`);
      }
    }

    // 验证端口范围
    const ports = [
      this.config.server.http.port,
      this.config.server.websocket.port,
    ];

    for (const port of ports) {
      if (port < 1 || port > 65535) {
        throw new Error(`Invalid port number: ${port}`);
      }
    }

    // 验证最大房间数
    if (this.config.client.maxRooms < 1 || this.config.client.maxRooms > 100) {
      throw new Error(`Invalid maxRooms value: ${this.config.client.maxRooms}`);
    }
  }

  /**
   * 获取内置默认配置
   */
  getBuiltInDefaults() {
    return {
      server: {
        http: {
          host: 'localhost',
          port: 3000,
        },
        websocket: {
          host: 'localhost',
          port: 8080,
        },
        heartbeatInterval: 30000,
      },
      client: {
        serverUrl: 'ws://localhost:8080',
        clientId: null,
        maxRooms: 10,
        proxyServer: 'http://127.0.0.1:8080',
        reconnectInterval: 5000,
        maxReconnectAttempts: 10,
        heartbeatInterval: 30000,
        windowOptions: {
          width: 1024,
          height: 768,
          show: false,
          webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
          },
        },
      },
      logging: {
        level: 'info',
        enableConsole: true,
        enableFile: false,
        logFile: 'logs/app.log',
      },
      features: {
        enableRealTimeEvents: true,
        enableEventStorage: false,
        enableWebhooks: false,
        enableMetrics: true,
      },
    };
  }

  /**
   * 深度合并对象
   */
  mergeDeep(target, source) {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.mergeDeep(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  /**
   * 获取嵌套值
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj);
  }

  /**
   * 获取当前配置
   */
  getConfig() {
    return this.config;
  }

  /**
   * 保存配置到文件
   */
  saveConfig(filename, config = null) {
    const configToSave = config || this.config;
    const filePath = path.join(this.configDir, filename);
    
    fs.writeFileSync(filePath, JSON.stringify(configToSave, null, 2), 'utf8');
    console.log(`Configuration saved to: ${filePath}`);
  }
}

// 创建全局配置实例
const configLoader = new ConfigLoader();

module.exports = {
  ConfigLoader,
  loadConfig: (environment) => configLoader.load(environment),
  getConfig: () => configLoader.getConfig(),
  saveConfig: (filename, config) => configLoader.saveConfig(filename, config),
};
