#!/usr/bin/env node

/**
 * 配置持久化测试脚本
 * 测试客户端配置的保存和恢复功能
 */

const WebSocket = require('ws');
const { ClientMessage, MessageType } = require('./protocol/websocket-protocol');

class ConfigPersistenceTest {
  constructor(serverUrl = 'ws://localhost:8080') {
    this.serverUrl = serverUrl;
    this.ws = null;
    this.isConnected = false;
    this.testClientId = `test_client_${Date.now()}`;
    this.testRooms = [
      { roomId: 'test_room_001', roomUrl: '@test_user_1' },
      { roomId: 'test_room_002', roomUrl: '@test_user_2' },
    ];
  }

  /**
   * 运行配置持久化测试
   */
  async runTest() {
    console.log('🧪 开始配置持久化测试...\n');
    
    try {
      // 第一次连接：保存配置
      console.log('📡 第一次连接 - 保存配置');
      await this.connectAndSaveConfig();
      
      // 等待一段时间
      console.log('⏳ 等待2秒...');
      await this.sleep(2000);
      
      // 断开连接
      console.log('🔌 断开连接');
      this.disconnect();
      
      // 等待一段时间
      console.log('⏳ 等待2秒...');
      await this.sleep(2000);
      
      // 第二次连接：验证配置恢复
      console.log('📡 第二次连接 - 验证配置恢复');
      await this.connectAndVerifyRestore();
      
      console.log('\n✅ 配置持久化测试完成');
      
    } catch (error) {
      console.error('❌ 测试失败:', error);
    } finally {
      this.disconnect();
    }
  }

  /**
   * 连接并保存配置
   */
  async connectAndSaveConfig() {
    await this.connect();
    
    // 发送客户端就绪消息
    const readyMessage = ClientMessage.clientReady({
      clientId: this.testClientId,
      version: '1.0.0',
      capabilities: ['tiktok', 'test'],
    });
    
    this.sendMessage(readyMessage);
    console.log(`✅ 发送客户端就绪消息: ${this.testClientId}`);
    
    // 等待一下
    await this.sleep(1000);
    
    // 保存配置
    const roomConfigs = this.testRooms.map(room => ({
      roomId: room.roomId,
      roomUrl: room.roomUrl,
      config: { autoReconnect: true, maxRetries: 3 },
      status: 'active',
    }));
    
    const configMessage = ClientMessage.saveConfig(roomConfigs);
    this.sendMessage(configMessage);
    console.log(`✅ 保存配置: ${roomConfigs.length} 个房间`);
    
    // 等待保存完成
    await this.sleep(1000);
  }

  /**
   * 连接并验证配置恢复
   */
  async connectAndVerifyRestore() {
    return new Promise(async (resolve, reject) => {
      let configRestored = false;
      
      // 设置消息监听
      const originalOnMessage = this.ws ? this.ws.onmessage : null;
      
      await this.connect();
      
      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          console.log('📨 收到消息:', message.type);
          
          if (message.type === MessageType.CONFIG_RESTORE) {
            const { roomConfigs } = message.data;
            console.log(`✅ 收到配置恢复消息: ${roomConfigs.length} 个房间`);
            
            // 验证恢复的配置
            if (this.verifyRestoredConfig(roomConfigs)) {
              console.log('✅ 配置恢复验证成功');
              configRestored = true;
              resolve();
            } else {
              reject(new Error('配置恢复验证失败'));
            }
          }
        } catch (error) {
          console.error('消息处理错误:', error);
        }
      };
      
      // 发送客户端就绪消息
      const readyMessage = ClientMessage.clientReady({
        clientId: this.testClientId,
        version: '1.0.0',
        capabilities: ['tiktok', 'test'],
      });
      
      this.sendMessage(readyMessage);
      console.log(`✅ 发送客户端就绪消息: ${this.testClientId}`);
      
      // 设置超时
      setTimeout(() => {
        if (!configRestored) {
          reject(new Error('配置恢复超时'));
        }
      }, 5000);
    });
  }

  /**
   * 验证恢复的配置
   */
  verifyRestoredConfig(roomConfigs) {
    console.log('🔍 验证恢复的配置...');
    
    if (roomConfigs.length !== this.testRooms.length) {
      console.error(`❌ 房间数量不匹配: 期望 ${this.testRooms.length}, 实际 ${roomConfigs.length}`);
      return false;
    }
    
    for (const testRoom of this.testRooms) {
      const restoredRoom = roomConfigs.find(r => r.room_id === testRoom.roomId);
      if (!restoredRoom) {
        console.error(`❌ 未找到房间: ${testRoom.roomId}`);
        return false;
      }
      
      if (restoredRoom.room_url !== testRoom.roomUrl) {
        console.error(`❌ 房间URL不匹配: ${testRoom.roomId}`);
        return false;
      }
      
      console.log(`✅ 房间验证通过: ${testRoom.roomId} -> ${testRoom.roomUrl}`);
    }
    
    return true;
  }

  /**
   * 连接到服务器
   */
  async connect() {
    return new Promise((resolve, reject) => {
      console.log(`📡 连接到: ${this.serverUrl}`);
      
      this.ws = new WebSocket(this.serverUrl);
      
      this.ws.on('open', () => {
        console.log('✅ WebSocket连接已建立');
        this.isConnected = true;
        resolve();
      });
      
      this.ws.on('error', (error) => {
        console.error('❌ WebSocket错误:', error);
        reject(error);
      });
      
      this.ws.on('close', () => {
        console.log('🔌 WebSocket连接已关闭');
        this.isConnected = false;
      });
      
      // 设置超时
      setTimeout(() => {
        if (!this.isConnected) {
          reject(new Error('连接超时'));
        }
      }, 5000);
    });
  }

  /**
   * 发送消息
   */
  sendMessage(message) {
    if (!this.isConnected || !this.ws) {
      throw new Error('未连接到服务器');
    }
    
    this.ws.send(JSON.stringify(message.toJSON()));
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
  }

  /**
   * 睡眠函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// HTTP API 测试
async function testHTTPAPI() {
  console.log('\n🌐 测试HTTP API...');
  
  try {
    // 测试数据库统计
    const statsResponse = await fetch('http://localhost:3000/api/stats');
    const statsData = await statsResponse.json();
    
    if (statsData.success) {
      console.log('✅ 数据库统计API正常');
      console.log('   统计信息:', statsData.data);
    } else {
      console.log('❌ 数据库统计API异常:', statsData.error);
    }
    
  } catch (error) {
    console.log('❌ HTTP API测试失败:', error.message);
    console.log('   请确保服务端已启动');
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const testType = args[0] || 'all';
  
  console.log('🚀 配置持久化测试');
  console.log('==================');
  
  try {
    switch (testType) {
      case 'persistence':
        const test = new ConfigPersistenceTest();
        await test.runTest();
        break;
        
      case 'api':
        await testHTTPAPI();
        break;
        
      case 'all':
      default:
        await testHTTPAPI();
        const persistenceTest = new ConfigPersistenceTest();
        await persistenceTest.runTest();
        break;
    }
    
  } catch (error) {
    console.error('测试执行失败:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { ConfigPersistenceTest };
