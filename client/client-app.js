/**
 * 客户端应用程序
 * 集成WebSocket客户端和房间管理器，实现完整的客户端功能
 */

const { app, BrowserWindow } = require('electron');
const WebSocketClient = require('./websocket-client');
const RoomManager = require('./room-manager');

class ClientApp {
  constructor(options = {}) {
    this.serverUrl = options.serverUrl || 'ws://localhost:8080';
    this.clientId = options.clientId || null;
    this.proxyServer = options.proxyServer || 'http://127.0.0.1:8080';
    this.maxRooms = options.maxRooms || 10;
    
    // 创建WebSocket客户端
    this.wsClient = new WebSocketClient({
      serverUrl: this.serverUrl,
      clientId: this.clientId,
    });
    
    // 创建房间管理器
    this.roomManager = new RoomManager({
      maxRooms: this.maxRooms,
      proxyServer: this.proxyServer,
      windowOptions: {
        width: 1024,
        height: 768,
        show: false, // 默认不显示窗口，可以通过配置控制
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
        },
      },
    });
    
    this.isRunning = false;
    this.setupEventHandlers();
  }

  /**
   * 设置事件处理器
   */
  setupEventHandlers() {
    // WebSocket客户端事件
    this.wsClient.on('connected', () => {
      console.log('Connected to server');
    });

    this.wsClient.on('disconnected', () => {
      console.log('Disconnected from server');
    });

    this.wsClient.on('error', (error) => {
      console.error('WebSocket error:', error);
    });

    this.wsClient.on('roomAdd', ({ roomId, roomUrl, config }) => {
      this.handleRoomAdd(roomId, roomUrl, config);
    });

    this.wsClient.on('roomRemove', ({ roomId }) => {
      this.handleRoomRemove(roomId);
    });

    this.wsClient.on('roomListRequest', () => {
      this.handleRoomListRequest();
    });

    this.wsClient.on('clientStatusRequest', () => {
      this.handleClientStatusRequest();
    });

    // 房间管理器事件
    this.roomManager.on('roomAdded', (roomInfo) => {
      console.log(`Room added: ${roomInfo.roomId}`);
      this.wsClient.sendRoomStatus(roomInfo.roomId, roomInfo.status);
    });

    this.roomManager.on('roomRemoved', ({ roomId }) => {
      console.log(`Room removed: ${roomId}`);
      this.wsClient.sendRoomStatus(roomId, 'disconnected');
    });

    this.roomManager.on('roomStatusUpdate', ({ roomId, status, details }) => {
      console.log(`Room ${roomId} status: ${status}`);
      this.wsClient.sendRoomStatus(roomId, status, details);
    });

    this.roomManager.on('liveEvent', ({ roomId, eventType, eventData }) => {
      // 发送直播间事件到服务端
      this.wsClient.sendLiveEvent(roomId, eventType, eventData);
      
      // 打印重要事件
      this.logImportantEvent(roomId, eventType, eventData);
    });

    this.roomManager.on('roomError', ({ roomId, error }) => {
      console.error(`Room ${roomId} error:`, error);
      this.wsClient.sendError(error, roomId);
    });

    this.roomManager.on('roomLog', ({ roomId, message }) => {
      console.log(`[${roomId}] ${message}`);
    });
  }

  /**
   * 启动客户端应用
   */
  async start() {
    if (this.isRunning) {
      console.log('Client app is already running');
      return;
    }

    try {
      console.log('Starting client app...');
      
      // 连接到服务器
      await this.wsClient.connect();
      
      this.isRunning = true;
      console.log('Client app started successfully');
      
    } catch (error) {
      console.error('Failed to start client app:', error);
      throw error;
    }
  }

  /**
   * 停止客户端应用
   */
  async stop() {
    if (!this.isRunning) {
      console.log('Client app is not running');
      return;
    }

    try {
      console.log('Stopping client app...');
      
      // 清理所有房间
      await this.roomManager.cleanup();
      
      // 断开WebSocket连接
      this.wsClient.disconnect();
      
      this.isRunning = false;
      console.log('Client app stopped');
      
    } catch (error) {
      console.error('Error stopping client app:', error);
    }
  }

  /**
   * 处理添加房间请求
   */
  async handleRoomAdd(roomId, roomUrl, config) {
    try {
      console.log(`Adding room: ${roomId} -> ${roomUrl}`);
      await this.roomManager.addRoom(roomId, roomUrl, config);
    } catch (error) {
      console.error(`Failed to add room ${roomId}:`, error);
      this.wsClient.sendError(error, roomId);
    }
  }

  /**
   * 处理移除房间请求
   */
  async handleRoomRemove(roomId) {
    try {
      console.log(`Removing room: ${roomId}`);
      const success = await this.roomManager.removeRoom(roomId);
      if (!success) {
        throw new Error(`Room ${roomId} not found`);
      }
    } catch (error) {
      console.error(`Failed to remove room ${roomId}:`, error);
      this.wsClient.sendError(error, roomId);
    }
  }

  /**
   * 处理房间列表请求
   */
  handleRoomListRequest() {
    const rooms = this.roomManager.getAllRooms();
    console.log('Room list requested:', rooms);
    
    // 可以发送房间列表到服务端（如果需要）
    // 这里暂时只打印日志
  }

  /**
   * 处理客户端状态请求
   */
  handleClientStatusRequest() {
    const status = {
      isRunning: this.isRunning,
      connection: this.wsClient.getConnectionStatus(),
      rooms: this.roomManager.getStats(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    };
    
    console.log('Client status requested:', status);
    
    // 可以发送状态到服务端（如果需要）
    // 这里暂时只打印日志
  }

  /**
   * 记录重要事件
   */
  logImportantEvent(roomId, eventType, eventData) {
    const importantEvents = ['gift', 'follow', 'live_end', 'live_start'];
    
    if (importantEvents.includes(eventType)) {
      console.log(`[IMPORTANT] ${eventType.toUpperCase()} in room ${roomId}:`, eventData);
    }
  }

  /**
   * 获取应用状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      connection: this.wsClient.getConnectionStatus(),
      rooms: this.roomManager.getStats(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    };
  }
}

// Electron应用程序设置
class ElectronClientApp extends ClientApp {
  constructor(options = {}) {
    super(options);
    
    // 设置代理
    if (this.proxyServer) {
      app.commandLine.appendSwitch('proxy-server', this.proxyServer);
    }
  }

  /**
   * 初始化Electron应用
   */
  async initializeElectron() {
    // 等待Electron准备就绪
    await app.whenReady();
    
    // 设置应用事件处理
    app.on('window-all-closed', () => {
      // 在macOS上，除非用户明确退出，否则保持应用运行
      if (process.platform !== 'darwin') {
        this.stop().then(() => {
          app.quit();
        });
      }
    });

    app.on('activate', () => {
      // 在macOS上，当点击dock图标且没有其他窗口打开时，重新创建窗口
      if (BrowserWindow.getAllWindows().length === 0) {
        // 这里可以创建一个管理界面窗口（可选）
      }
    });

    app.on('before-quit', async () => {
      await this.stop();
    });
  }

  /**
   * 启动Electron客户端应用
   */
  async start() {
    try {
      // 初始化Electron
      await this.initializeElectron();
      
      // 启动客户端应用
      await super.start();
      
      console.log('Electron client app started successfully');
      
    } catch (error) {
      console.error('Failed to start Electron client app:', error);
      throw error;
    }
  }
}

module.exports = { ClientApp, ElectronClientApp };
