/**
 * 房间管理器
 * 负责管理多个直播间的监听，包括浏览器页面的创建和销毁
 */

const EventEmitter = require('events');
const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const { startMonitorPage } = require('../src/tiktok');
const { RoomStatus, LiveEvent, LiveEventType } = require('../protocol/websocket-protocol');

// 使用stealth插件避免被检测
puppeteer.use(StealthPlugin());

class RoomManager extends EventEmitter {
  constructor(options = {}) {
    super();

    this.rooms = new Map(); // roomId -> { browser, page, status, config, url, startTime }
    this.maxRooms = options.maxRooms || 10;
    this.browserOptions = options.browserOptions || {
      headless: options.headless !== false, // 默认无头模式
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
      ],
    };

    // 代理设置（如果需要）
    this.proxyServer = options.proxyServer || 'http://127.0.0.1:8080';
    if (this.proxyServer) {
      this.browserOptions.args.push(`--proxy-server=${this.proxyServer}`);
    }

    // 全局浏览器实例（可选，用于共享浏览器）
    this.sharedBrowser = null;
    this.useSharedBrowser = options.useSharedBrowser !== false;
  }

  /**
   * 添加房间监听
   */
  async addRoom(roomId, roomUrl, config = {}) {
    if (this.rooms.has(roomId)) {
      throw new Error(`Room ${roomId} is already being monitored`);
    }

    if (this.rooms.size >= this.maxRooms) {
      throw new Error(`Maximum number of rooms (${this.maxRooms}) reached`);
    }

    try {
      console.log(`Adding room: ${roomId} -> ${roomUrl}`);

      // 获取或创建浏览器实例
      let browser;
      if (this.useSharedBrowser) {
        browser = await this.getSharedBrowser();
      } else {
        browser = await puppeteer.launch(this.browserOptions);
      }

      // 创建新页面
      const page = await browser.newPage();

      // 设置用户代理
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

      // 设置视口
      await page.setViewport({ width: 1920, height: 1080 });

      const roomInfo = {
        roomId,
        roomUrl,
        browser: this.useSharedBrowser ? null : browser, // 如果使用共享浏览器，不存储browser引用
        page,
        config,
        status: RoomStatus.CONNECTING,
        startTime: Date.now(),
        eventCount: 0,
        lastEventTime: null,
      };

      this.rooms.set(roomId, roomInfo);

      // 设置页面事件处理
      this.setupPageEvents(roomInfo);

      // 开始监听
      await this.startMonitoring(roomInfo);

      this.emit('roomAdded', roomInfo);
      return roomInfo;

    } catch (error) {
      console.error(`Failed to add room ${roomId}:`, error);
      this.emit('roomError', { roomId, error });
      throw error;
    }
  }

  /**
   * 移除房间监听
   */
  async removeRoom(roomId) {
    const roomInfo = this.rooms.get(roomId);
    if (!roomInfo) {
      return false;
    }

    try {
      console.log(`Removing room: ${roomId}`);

      // 关闭页面
      if (roomInfo.page && !roomInfo.page.isClosed()) {
        await roomInfo.page.close();
      }

      // 如果不是共享浏览器，关闭浏览器实例
      if (roomInfo.browser) {
        await roomInfo.browser.close();
      }

      this.rooms.delete(roomId);
      this.emit('roomRemoved', { roomId });
      return true;

    } catch (error) {
      console.error(`Failed to remove room ${roomId}:`, error);
      this.emit('roomError', { roomId, error });
      return false;
    }
  }

  /**
   * 获取房间信息
   */
  getRoom(roomId) {
    const roomInfo = this.rooms.get(roomId);
    if (!roomInfo) return null;

    return {
      roomId: roomInfo.roomId,
      roomUrl: roomInfo.roomUrl,
      status: roomInfo.status,
      config: roomInfo.config,
      startTime: roomInfo.startTime,
      eventCount: roomInfo.eventCount,
      lastEventTime: roomInfo.lastEventTime,
      uptime: Date.now() - roomInfo.startTime,
    };
  }

  /**
   * 获取所有房间信息
   */
  getAllRooms() {
    return Array.from(this.rooms.keys()).map(roomId => this.getRoom(roomId));
  }

  /**
   * 设置页面事件处理
   */
  setupPageEvents(roomInfo) {
    const { roomId, page } = roomInfo;

    page.on('close', () => {
      console.log(`Page closed for room: ${roomId}`);
      this.rooms.delete(roomId);
      this.emit('roomRemoved', { roomId });
    });

    page.on('error', (error) => {
      console.error(`Page error for room ${roomId}:`, error);
      this.updateRoomStatus(roomId, RoomStatus.ERROR, { error: error.message });
      this.emit('roomError', { roomId, error });
    });

    page.on('pageerror', (error) => {
      console.error(`Page script error for room ${roomId}:`, error);
      this.updateRoomStatus(roomId, RoomStatus.ERROR, { error: error.message });
    });

    page.on('requestfailed', (request) => {
      console.warn(`Request failed for room ${roomId}:`, request.url(), request.failure().errorText);
    });

    page.on('response', (response) => {
      if (!response.ok()) {
        console.warn(`HTTP error for room ${roomId}:`, response.status(), response.url());
      }
    });
  }

  /**
   * 开始监听房间
   */
  async startMonitoring(roomInfo) {
    const { roomId, roomUrl, page } = roomInfo;

    try {
      // 更新状态为连接中
      this.updateRoomStatus(roomId, RoomStatus.CONNECTING);

      // 导航到直播间页面
      const tiktokUrl = this.buildTikTokUrl(roomUrl);
      console.log(`Navigating to: ${tiktokUrl}`);

      await page.goto(tiktokUrl, {
        waitUntil: 'networkidle2',
        timeout: 30000
      });

      // 等待页面加载完成
      await page.waitForTimeout(3000);

      // 使用原有的监听逻辑
      const liveId = this.extractLiveId(roomUrl);
      console.log(`Starting monitor for live ID: ${liveId}`);

      await startMonitorPage(
        page,
        liveId,
        (payload) => this.handleTikTokEvent(roomId, payload),
        (info) => this.handleLogCallback(roomId, info)
      );

      // 更新状态为监听中
      this.updateRoomStatus(roomId, RoomStatus.MONITORING);
      console.log(`Successfully started monitoring room: ${roomId}`);

    } catch (error) {
      console.error(`Failed to start monitoring room ${roomId}:`, error);
      this.updateRoomStatus(roomId, RoomStatus.ERROR, { error: error.message });
      throw error;
    }
  }

  /**
   * 处理TikTok事件
   */
  handleTikTokEvent(roomId, payload) {
    const roomInfo = this.rooms.get(roomId);
    if (!roomInfo) return;

    try {
      const payloadJson = JSON.stringify(payload);
      const events = JSON.parse(payloadJson).payload;

      events.forEach(event => {
        const liveEvent = this.convertTikTokEvent(event);
        if (liveEvent) {
          roomInfo.eventCount++;
          roomInfo.lastEventTime = Date.now();

          this.emit('liveEvent', {
            roomId,
            eventType: liveEvent.type,
            eventData: liveEvent,
          });
        }
      });

    } catch (error) {
      console.error(`Error handling TikTok event for room ${roomId}:`, error);
      this.emit('roomError', { roomId, error });
    }
  }

  /**
   * 转换TikTok事件为标准格式
   */
  convertTikTokEvent(event) {
    const msgType = Number(event.common?.msgType);

    switch (msgType) {
      case 1: // 点赞
        return LiveEvent.like(
          event.user,
          event.likeNum,
          event.likeTotal
        );

      case 2: // 评论
        return LiveEvent.comment(
          event.user,
          event.content,
          event.contentLanguage
        );

      case 3: // 礼物
        return LiveEvent.gift(event.user, {
          giftId: event.giftId,
          giftName: event.giftName,
          groupCount: event.groupCount,
          comboCount: event.comboCount,
          combo: event.combo,
          repeatEnd: event.repeatEnd,
        });

      case 4: // 粉丝团
        if (Number(event.action) === 2) {
          return LiveEvent.fanclubJoin(event.user);
        } else if (Number(event.action) === 1) {
          return LiveEvent.fanclubUpgrade(event.user, event.user.fansclubLevel);
        }
        break;

      case 5: // 关注
        if (Number(event.action) === 1) {
          return LiveEvent.follow(event.user, event.followCount);
        }
        break;

      case 7: // 用户进房
        return LiveEvent.memberJoin(event.user, event.memberCount);

      case 1000: // 直播间状态
        const action = Number(event.action);
        if (action === 3) {
          return LiveEvent.liveEnd(event.common.roomId);
        } else if (action === 1) {
          return LiveEvent.livePause(event.common.roomId);
        } else if (action === 2) {
          return LiveEvent.liveResume(event.common.roomId);
        }
        break;

      case 1001: // 开始监听时的状态
        const statusAction = Number(event.action);
        if (statusAction === 100) {
          return LiveEvent.liveStart(event.common.roomId);
        }
        break;
    }

    return null;
  }

  /**
   * 构建TikTok URL
   */
  buildTikTokUrl(roomUrl) {
    // 如果是用户名格式，构建完整URL
    if (roomUrl.startsWith('@')) {
      return `https://www.tiktok.com/${roomUrl}/live`;
    }

    // 如果已经是完整URL，直接使用
    if (roomUrl.startsWith('http')) {
      return roomUrl;
    }

    // 其他格式的处理
    return `https://www.tiktok.com/@${roomUrl}/live`;
  }



  /**
   * 处理日志回调
   */
  handleLogCallback(roomId, info) {
    console.log(`[${roomId}] ${info}`);
    this.emit('roomLog', { roomId, message: info });
  }

  /**
   * 更新房间状态
   */
  updateRoomStatus(roomId, status, details = {}) {
    const roomInfo = this.rooms.get(roomId);
    if (roomInfo) {
      roomInfo.status = status;
      this.emit('roomStatusUpdate', { roomId, status, details });
    }
  }

  /**
   * 从URL提取直播ID
   */
  extractLiveId(roomUrl) {
    // 支持不同格式的URL
    if (roomUrl.includes('@')) {
      return roomUrl.split('@')[1];
    }
    
    // 可以添加更多URL格式的支持
    return roomUrl;
  }

  /**
   * 获取共享浏览器实例
   */
  async getSharedBrowser() {
    if (!this.sharedBrowser || this.sharedBrowser.isConnected() === false) {
      console.log('Creating shared browser instance...');
      this.sharedBrowser = await puppeteer.launch(this.browserOptions);

      // 监听浏览器关闭事件
      this.sharedBrowser.on('disconnected', () => {
        console.log('Shared browser disconnected');
        this.sharedBrowser = null;
      });
    }

    return this.sharedBrowser;
  }

  /**
   * 清理所有房间
   */
  async cleanup() {
    console.log('Cleaning up all rooms...');

    const roomIds = Array.from(this.rooms.keys());
    for (const roomId of roomIds) {
      await this.removeRoom(roomId);
    }

    // 关闭共享浏览器
    if (this.sharedBrowser) {
      await this.sharedBrowser.close();
      this.sharedBrowser = null;
    }

    console.log('All rooms cleaned up');
  }

  /**
   * 获取统计信息
   */
  getStats() {
    const rooms = this.getAllRooms();
    
    return {
      totalRooms: rooms.length,
      maxRooms: this.maxRooms,
      totalEvents: rooms.reduce((sum, room) => sum + room.eventCount, 0),
      roomsByStatus: rooms.reduce((acc, room) => {
        acc[room.status] = (acc[room.status] || 0) + 1;
        return acc;
      }, {}),
    };
  }
}

module.exports = RoomManager;
