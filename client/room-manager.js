/**
 * 房间管理器
 * 负责管理多个直播间的监听，包括浏览器页面的创建和销毁
 */

const EventEmitter = require('events');
const { BrowserWindow } = require('electron');
const { RoomStatus, LiveEvent, LiveEventType } = require('../protocol/websocket-protocol');

class RoomManager extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.rooms = new Map(); // roomId -> { window, status, config, url, startTime }
    this.maxRooms = options.maxRooms || 10;
    this.windowOptions = options.windowOptions || {
      width: 1024,
      height: 768,
      show: false, // 默认不显示窗口
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
      },
    };
    
    // 代理设置（如果需要）
    this.proxyServer = options.proxyServer || 'http://127.0.0.1:8080';
  }

  /**
   * 添加房间监听
   */
  async addRoom(roomId, roomUrl, config = {}) {
    if (this.rooms.has(roomId)) {
      throw new Error(`Room ${roomId} is already being monitored`);
    }

    if (this.rooms.size >= this.maxRooms) {
      throw new Error(`Maximum number of rooms (${this.maxRooms}) reached`);
    }

    try {
      console.log(`Adding room: ${roomId} -> ${roomUrl}`);
      
      // 创建浏览器窗口
      const window = new BrowserWindow({
        ...this.windowOptions,
        title: `TikTok Live Monitor - ${roomId}`,
      });

      // 设置代理（如果配置了）
      if (this.proxyServer && config.useProxy !== false) {
        await window.webContents.session.setProxy({
          proxyRules: this.proxyServer,
        });
      }

      const roomInfo = {
        roomId,
        roomUrl,
        window,
        config,
        status: RoomStatus.CONNECTING,
        startTime: Date.now(),
        eventCount: 0,
        lastEventTime: null,
      };

      this.rooms.set(roomId, roomInfo);

      // 设置窗口事件处理
      this.setupWindowEvents(roomInfo);

      // 开始监听
      await this.startMonitoring(roomInfo);

      this.emit('roomAdded', roomInfo);
      return roomInfo;

    } catch (error) {
      console.error(`Failed to add room ${roomId}:`, error);
      this.emit('roomError', { roomId, error });
      throw error;
    }
  }

  /**
   * 移除房间监听
   */
  async removeRoom(roomId) {
    const roomInfo = this.rooms.get(roomId);
    if (!roomInfo) {
      return false;
    }

    try {
      console.log(`Removing room: ${roomId}`);

      // 清理模拟定时器
      if (roomInfo.simulationTimer) {
        clearInterval(roomInfo.simulationTimer);
        roomInfo.simulationTimer = null;
      }

      // 分离调试器
      if (roomInfo.window && !roomInfo.window.isDestroyed()) {
        try {
          if (roomInfo.window.webContents.debugger.isAttached()) {
            roomInfo.window.webContents.debugger.detach();
          }
        } catch (debuggerError) {
          console.warn(`Failed to detach debugger for room ${roomId}:`, debuggerError);
        }

        // 关闭浏览器窗口
        roomInfo.window.close();
      }

      this.rooms.delete(roomId);
      this.emit('roomRemoved', { roomId });
      return true;

    } catch (error) {
      console.error(`Failed to remove room ${roomId}:`, error);
      this.emit('roomError', { roomId, error });
      return false;
    }
  }

  /**
   * 获取房间信息
   */
  getRoom(roomId) {
    const roomInfo = this.rooms.get(roomId);
    if (!roomInfo) return null;

    return {
      roomId: roomInfo.roomId,
      roomUrl: roomInfo.roomUrl,
      status: roomInfo.status,
      config: roomInfo.config,
      startTime: roomInfo.startTime,
      eventCount: roomInfo.eventCount,
      lastEventTime: roomInfo.lastEventTime,
      uptime: Date.now() - roomInfo.startTime,
    };
  }

  /**
   * 获取所有房间信息
   */
  getAllRooms() {
    return Array.from(this.rooms.keys()).map(roomId => this.getRoom(roomId));
  }

  /**
   * 设置窗口事件处理
   */
  setupWindowEvents(roomInfo) {
    const { roomId, window } = roomInfo;

    window.on('closed', () => {
      console.log(`Window closed for room: ${roomId}`);
      this.rooms.delete(roomId);
      this.emit('roomRemoved', { roomId });
    });

    window.on('unresponsive', () => {
      console.warn(`Window unresponsive for room: ${roomId}`);
      this.updateRoomStatus(roomId, RoomStatus.ERROR, { error: 'Window unresponsive' });
    });

    window.on('responsive', () => {
      console.log(`Window responsive again for room: ${roomId}`);
      this.updateRoomStatus(roomId, RoomStatus.MONITORING);
    });

    window.webContents.on('crashed', () => {
      console.error(`Window crashed for room: ${roomId}`);
      this.updateRoomStatus(roomId, RoomStatus.ERROR, { error: 'Window crashed' });
      this.emit('roomError', { roomId, error: new Error('Window crashed') });
    });
  }

  /**
   * 开始监听房间
   */
  async startMonitoring(roomInfo) {
    const { roomId, roomUrl, window } = roomInfo;

    try {
      // 更新状态为连接中
      this.updateRoomStatus(roomId, RoomStatus.CONNECTING);

      // 设置WebSocket监听
      await this.setupWebSocketMonitoring(roomInfo);

      // 导航到直播间页面
      await window.loadURL(this.buildTikTokUrl(roomUrl));

      // 更新状态为监听中
      this.updateRoomStatus(roomId, RoomStatus.MONITORING);

    } catch (error) {
      console.error(`Failed to start monitoring room ${roomId}:`, error);
      this.updateRoomStatus(roomId, RoomStatus.ERROR, { error: error.message });
      throw error;
    }
  }

  /**
   * 设置WebSocket监听
   */
  async setupWebSocketMonitoring(roomInfo) {
    const { roomId, window } = roomInfo;
    const webContents = window.webContents;

    // 启用网络域
    const session = await webContents.debugger.attach('1.3');
    await webContents.debugger.sendCommand('Network.enable');

    // 监听WebSocket创建
    webContents.debugger.on('message', (event, method, params) => {
      if (method === 'Network.webSocketCreated') {
        console.log(`[${roomId}] WebSocket created:`, params.url);
      } else if (method === 'Network.webSocketFrameReceived') {
        this.handleWebSocketFrame(roomId, params);
      } else if (method === 'Network.webSocketClosed') {
        console.log(`[${roomId}] WebSocket closed`);
        this.updateRoomStatus(roomId, RoomStatus.DISCONNECTED);
      }
    });

    webContents.debugger.on('detach', (event, reason) => {
      console.log(`[${roomId}] Debugger detached:`, reason);
    });
  }

  /**
   * 处理WebSocket帧数据
   */
  handleWebSocketFrame(roomId, params) {
    try {
      // 这里需要实现WebSocket帧解析逻辑
      // 由于复杂性，我们先简化处理
      const payloadData = params.response?.payloadData;
      if (payloadData) {
        // 模拟事件数据处理
        this.simulateEventHandling(roomId);
      }
    } catch (error) {
      console.error(`Error handling WebSocket frame for room ${roomId}:`, error);
    }
  }

  /**
   * 模拟事件处理（简化版本）
   */
  simulateEventHandling(roomId) {
    const roomInfo = this.rooms.get(roomId);
    if (!roomInfo) return;

    // 这是一个简化的实现，实际应该解析真实的WebSocket数据
    // 为了演示，我们定期生成一些模拟事件
    if (!roomInfo.simulationTimer) {
      roomInfo.simulationTimer = setInterval(() => {
        // 模拟不同类型的事件
        const eventTypes = ['like', 'comment', 'member_join'];
        const randomType = eventTypes[Math.floor(Math.random() * eventTypes.length)];

        const mockEvent = this.createMockEvent(randomType);
        if (mockEvent) {
          roomInfo.eventCount++;
          roomInfo.lastEventTime = Date.now();

          this.emit('liveEvent', {
            roomId,
            eventType: mockEvent.type,
            eventData: mockEvent,
          });
        }
      }, 10000 + Math.random() * 20000); // 10-30秒随机间隔
    }
  }

  /**
   * 创建模拟事件（用于测试）
   */
  createMockEvent(eventType) {
    const mockUser = {
      userId: `user_${Math.random().toString(36).substr(2, 9)}`,
      nickname: `用户${Math.floor(Math.random() * 1000)}`,
      displayId: `${Math.floor(Math.random() * 100000)}`,
    };

    switch (eventType) {
      case 'like':
        return LiveEvent.like(mockUser, Math.floor(Math.random() * 10) + 1, Math.floor(Math.random() * 1000));
      case 'comment':
        return LiveEvent.comment(mockUser, `这是一条测试评论 ${Date.now()}`);
      case 'member_join':
        return LiveEvent.memberJoin(mockUser, Math.floor(Math.random() * 100) + 50);
      default:
        return null;
    }
  }

  /**
   * 构建TikTok URL
   */
  buildTikTokUrl(roomUrl) {
    // 如果是用户名格式，构建完整URL
    if (roomUrl.startsWith('@')) {
      return `https://www.tiktok.com/${roomUrl}/live`;
    }

    // 如果已经是完整URL，直接使用
    if (roomUrl.startsWith('http')) {
      return roomUrl;
    }

    // 其他格式的处理
    return `https://www.tiktok.com/@${roomUrl}/live`;
  }



  /**
   * 处理日志回调
   */
  handleLogCallback(roomId, info) {
    console.log(`[${roomId}] ${info}`);
    this.emit('roomLog', { roomId, message: info });
  }

  /**
   * 更新房间状态
   */
  updateRoomStatus(roomId, status, details = {}) {
    const roomInfo = this.rooms.get(roomId);
    if (roomInfo) {
      roomInfo.status = status;
      this.emit('roomStatusUpdate', { roomId, status, details });
    }
  }

  /**
   * 从URL提取直播ID
   */
  extractLiveId(roomUrl) {
    // 支持不同格式的URL
    if (roomUrl.includes('@')) {
      return roomUrl.split('@')[1];
    }
    
    // 可以添加更多URL格式的支持
    return roomUrl;
  }

  /**
   * 清理所有房间
   */
  async cleanup() {
    console.log('Cleaning up all rooms...');
    
    const roomIds = Array.from(this.rooms.keys());
    for (const roomId of roomIds) {
      await this.removeRoom(roomId);
    }
    
    console.log('All rooms cleaned up');
  }

  /**
   * 获取统计信息
   */
  getStats() {
    const rooms = this.getAllRooms();
    
    return {
      totalRooms: rooms.length,
      maxRooms: this.maxRooms,
      totalEvents: rooms.reduce((sum, room) => sum + room.eventCount, 0),
      roomsByStatus: rooms.reduce((acc, room) => {
        acc[room.status] = (acc[room.status] || 0) + 1;
        return acc;
      }, {}),
    };
  }
}

module.exports = RoomManager;
