/**
 * WebSocket 客户端
 * 负责与服务端通信，接收房间管理指令
 */

const WebSocket = require('ws');
const EventEmitter = require('events');
const { 
  MessageType, 
  ClientMessage, 
  BaseMessage,
  RoomStatus 
} = require('../protocol/websocket-protocol');

class WebSocketClient extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.serverUrl = options.serverUrl || 'ws://localhost:8080';
    this.clientId = options.clientId || this.generateClientId();
    this.reconnectInterval = options.reconnectInterval || 5000;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
    this.heartbeatInterval = options.heartbeatInterval || 30000;
    
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.reconnectTimer = null;
    this.heartbeatTimer = null;
    
    // 消息处理器
    this.messageHandlers = new Map();
    this.setupMessageHandlers();
  }

  /**
   * 连接到服务器
   */
  async connect() {
    return new Promise((resolve, reject) => {
      try {
        console.log(`Connecting to ${this.serverUrl}...`);
        
        this.ws = new WebSocket(this.serverUrl);
        
        this.ws.on('open', () => {
          console.log('Connected to server');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          
          // 发送客户端就绪消息
          this.sendClientReady();
          
          // 启动心跳
          this.startHeartbeat();
          
          this.emit('connected');
          resolve();
        });
        
        this.ws.on('message', (data) => {
          this.handleMessage(data);
        });
        
        this.ws.on('close', (code, reason) => {
          console.log(`Connection closed: ${code} ${reason}`);
          this.handleDisconnection();
        });
        
        this.ws.on('error', (error) => {
          console.error('WebSocket error:', error);
          this.emit('error', error);
          
          if (!this.isConnected) {
            reject(error);
          }
        });
        
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 断开连接
   */
  disconnect() {
    this.isConnected = false;
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  /**
   * 处理断开连接
   */
  handleDisconnection() {
    this.isConnected = false;
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
    
    this.emit('disconnected');
    
    // 自动重连
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      
      this.reconnectTimer = setTimeout(() => {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
        });
      }, this.reconnectInterval);
    } else {
      console.error('Max reconnection attempts reached');
      this.emit('maxReconnectAttemptsReached');
    }
  }

  /**
   * 处理消息
   */
  handleMessage(data) {
    try {
      const message = BaseMessage.fromJSON(data);
      const handler = this.messageHandlers.get(message.type);
      
      if (handler) {
        handler.call(this, message);
      } else {
        console.warn(`Unknown message type: ${message.type}`);
      }
    } catch (error) {
      console.error('Message handling error:', error);
    }
  }

  /**
   * 设置消息处理器
   */
  setupMessageHandlers() {
    this.messageHandlers.set(MessageType.ROOM_ADD, this.handleRoomAdd);
    this.messageHandlers.set(MessageType.ROOM_REMOVE, this.handleRoomRemove);
    this.messageHandlers.set(MessageType.ROOM_LIST, this.handleRoomList);
    this.messageHandlers.set(MessageType.CLIENT_STATUS, this.handleClientStatus);
  }

  /**
   * 处理添加房间消息
   */
  handleRoomAdd(message) {
    const { roomId, roomUrl, config } = message.data;
    console.log(`Received room add request: ${roomId} -> ${roomUrl}`);
    
    this.emit('roomAdd', { roomId, roomUrl, config });
  }

  /**
   * 处理移除房间消息
   */
  handleRoomRemove(message) {
    const { roomId } = message.data;
    console.log(`Received room remove request: ${roomId}`);
    
    this.emit('roomRemove', { roomId });
  }

  /**
   * 处理房间列表请求
   */
  handleRoomList(message) {
    console.log('Received room list request');
    this.emit('roomListRequest');
  }

  /**
   * 处理客户端状态请求
   */
  handleClientStatus(message) {
    console.log('Received client status request');
    this.emit('clientStatusRequest');
  }

  /**
   * 发送消息
   */
  sendMessage(message) {
    if (!this.isConnected || !this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('Cannot send message: not connected');
      return false;
    }

    try {
      this.ws.send(JSON.stringify(message.toJSON()));
      return true;
    } catch (error) {
      console.error('Failed to send message:', error);
      return false;
    }
  }

  /**
   * 发送客户端就绪消息
   */
  sendClientReady() {
    const message = ClientMessage.clientReady({
      clientId: this.clientId,
      version: '1.0.0',
      capabilities: ['tiktok', 'douyin'],
    });
    
    this.sendMessage(message);
  }

  /**
   * 发送房间状态更新
   */
  sendRoomStatus(roomId, status, details = {}) {
    const message = ClientMessage.roomStatus(roomId, status, details);
    this.sendMessage(message);
  }

  /**
   * 发送直播间事件
   */
  sendLiveEvent(roomId, eventType, eventData) {
    const message = ClientMessage.liveEvent(roomId, eventType, eventData);
    this.sendMessage(message);
  }

  /**
   * 发送错误消息
   */
  sendError(error, roomId = null) {
    const message = ClientMessage.error(error, roomId);
    this.sendMessage(message);
  }

  /**
   * 发送心跳
   */
  sendHeartbeat() {
    const message = ClientMessage.heartbeat();
    this.sendMessage(message);
  }

  /**
   * 启动心跳
   */
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      this.sendHeartbeat();
    }, this.heartbeatInterval);
  }

  /**
   * 生成客户端ID
   */
  generateClientId() {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      clientId: this.clientId,
      serverUrl: this.serverUrl,
      reconnectAttempts: this.reconnectAttempts,
    };
  }
}

module.exports = WebSocketClient;
