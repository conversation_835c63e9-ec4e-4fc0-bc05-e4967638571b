<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok Live Monitor - 管理界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status-card h3 {
            color: #34495e;
            margin-bottom: 10px;
        }
        
        .status-value {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
        }
        
        .room-management {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .rooms-list {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .rooms-list h3 {
            padding: 20px;
            margin: 0;
            border-bottom: 1px solid #eee;
        }
        
        .room-item {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .room-item:last-child {
            border-bottom: none;
        }
        
        .room-info {
            flex: 1;
        }
        
        .room-id {
            font-weight: 500;
            color: #2c3e50;
        }
        
        .room-url {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .room-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-right: 10px;
        }
        
        .status-monitoring {
            background: #d5f4e6;
            color: #27ae60;
        }
        
        .status-connecting {
            background: #fef9e7;
            color: #f39c12;
        }
        
        .status-error {
            background: #fadbd8;
            color: #e74c3c;
        }
        
        .status-disconnected {
            background: #eaecee;
            color: #95a5a6;
        }
        
        .events-log {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        
        .events-log h3 {
            padding: 20px;
            margin: 0;
            border-bottom: 1px solid #eee;
        }
        
        .log-container {
            height: 300px;
            overflow-y: auto;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background: #f8f9fa;
        }
        
        .log-entry {
            padding: 2px 0;
            border-bottom: 1px solid #eee;
        }
        
        .log-timestamp {
            color: #6c757d;
        }
        
        .log-room {
            color: #007bff;
            font-weight: bold;
        }
        
        .log-event {
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>TikTok Live Monitor 管理界面</h1>
            <p>实时监控直播间数据，通过WebSocket与服务端通信</p>
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <h3>服务器状态</h3>
                <div class="status-value" id="serverStatus">检查中...</div>
            </div>
            <div class="status-card">
                <h3>连接的客户端</h3>
                <div class="status-value" id="clientCount">0</div>
            </div>
            <div class="status-card">
                <h3>监听房间数</h3>
                <div class="status-value" id="roomCount">0</div>
            </div>
            <div class="status-card">
                <h3>运行时间</h3>
                <div class="status-value" id="uptime">0s</div>
            </div>
            <div class="status-card">
                <h3>配置持久化</h3>
                <div class="status-value" id="configPersistence">未知</div>
            </div>
            <div class="status-card">
                <h3>数据库记录</h3>
                <div class="status-value" id="dbRecords">0</div>
            </div>
        </div>
        
        <div class="room-management">
            <h3>添加监听房间</h3>
            <div class="form-group">
                <label for="roomId">房间ID:</label>
                <input type="text" id="roomId" placeholder="例如: room_001">
            </div>
            <div class="form-group">
                <label for="roomUrl">房间URL:</label>
                <input type="text" id="roomUrl" placeholder="例如: @username 或完整URL">
            </div>
            <button class="btn" onclick="addRoom()">添加房间</button>
            <button class="btn" onclick="refreshData()">刷新数据</button>
        </div>
        
        <div class="rooms-list">
            <h3>监听房间列表</h3>
            <div id="roomsList">
                <div style="padding: 20px; text-align: center; color: #7f8c8d;">
                    暂无监听房间
                </div>
            </div>
        </div>
        
        <div class="events-log">
            <h3>事件日志</h3>
            <div class="log-container" id="logContainer">
                <div class="log-entry">
                    <span class="log-timestamp">[系统]</span> 管理界面已加载
                </div>
            </div>
        </div>
    </div>

    <script>
        let apiBase = '';
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            setInterval(refreshData, 5000); // 每5秒刷新一次
        });
        
        // 刷新数据
        async function refreshData() {
            try {
                await Promise.all([
                    updateServerStatus(),
                    updateRoomsList(),
                    updateDatabaseStats()
                ]);
            } catch (error) {
                console.error('刷新数据失败:', error);
                addLogEntry('系统', '刷新数据失败: ' + error.message);
            }
        }
        
        // 更新服务器状态
        async function updateServerStatus() {
            try {
                const response = await fetch('/api/status');
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('serverStatus').textContent = '运行中';
                    document.getElementById('clientCount').textContent = result.data.clients;
                    document.getElementById('roomCount').textContent = result.data.rooms;
                    document.getElementById('uptime').textContent = formatUptime(result.data.uptime);
                } else {
                    document.getElementById('serverStatus').textContent = '错误';
                }
            } catch (error) {
                document.getElementById('serverStatus').textContent = '离线';
                document.getElementById('configPersistence').textContent = '未知';
                document.getElementById('dbRecords').textContent = '0';
            }
        }

        // 更新数据库统计
        async function updateDatabaseStats() {
            try {
                const response = await fetch('/api/stats');
                const result = await response.json();

                if (result.success) {
                    document.getElementById('configPersistence').textContent = '启用';
                    const totalRecords = Object.values(result.data).reduce((sum, count) => sum + count, 0);
                    document.getElementById('dbRecords').textContent = totalRecords;
                } else {
                    document.getElementById('configPersistence').textContent = '禁用';
                    document.getElementById('dbRecords').textContent = '0';
                }
            } catch (error) {
                document.getElementById('configPersistence').textContent = '错误';
                document.getElementById('dbRecords').textContent = '0';
            }
        }
        
        // 更新房间列表
        async function updateRoomsList() {
            try {
                const response = await fetch('/api/rooms');
                const result = await response.json();
                
                const roomsList = document.getElementById('roomsList');
                
                if (result.success && result.data.length > 0) {
                    roomsList.innerHTML = result.data.map(room => `
                        <div class="room-item">
                            <div class="room-info">
                                <div class="room-id">${room.roomId}</div>
                                <div class="room-url">${room.roomUrl}</div>
                            </div>
                            <div>
                                <span class="room-status status-${room.status}">${getStatusText(room.status)}</span>
                                <button class="btn btn-danger" onclick="removeRoom('${room.roomId}')">删除</button>
                            </div>
                        </div>
                    `).join('');
                } else {
                    roomsList.innerHTML = '<div style="padding: 20px; text-align: center; color: #7f8c8d;">暂无监听房间</div>';
                }
            } catch (error) {
                console.error('更新房间列表失败:', error);
            }
        }
        
        // 添加房间
        async function addRoom() {
            const roomId = document.getElementById('roomId').value.trim();
            const roomUrl = document.getElementById('roomUrl').value.trim();
            
            if (!roomId || !roomUrl) {
                alert('请填写房间ID和URL');
                return;
            }
            
            try {
                const response = await fetch('/api/rooms', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ roomId, roomUrl })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLogEntry('系统', `成功添加房间: ${roomId} -> ${roomUrl}`);
                    document.getElementById('roomId').value = '';
                    document.getElementById('roomUrl').value = '';
                    refreshData();
                } else {
                    alert('添加房间失败: ' + result.error);
                    addLogEntry('系统', `添加房间失败: ${result.error}`);
                }
            } catch (error) {
                alert('添加房间失败: ' + error.message);
                addLogEntry('系统', `添加房间失败: ${error.message}`);
            }
        }
        
        // 删除房间
        async function removeRoom(roomId) {
            if (!confirm(`确定要删除房间 ${roomId} 吗？`)) {
                return;
            }
            
            try {
                const response = await fetch(`/api/rooms/${roomId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLogEntry('系统', `成功删除房间: ${roomId}`);
                    refreshData();
                } else {
                    alert('删除房间失败: ' + result.error);
                    addLogEntry('系统', `删除房间失败: ${result.error}`);
                }
            } catch (error) {
                alert('删除房间失败: ' + error.message);
                addLogEntry('系统', `删除房间失败: ${error.message}`);
            }
        }
        
        // 添加日志条目
        function addLogEntry(source, message) {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-room">[${source}]</span>
                <span class="log-event">${message}</span>
            `;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 限制日志条目数量
            const entries = logContainer.children;
            if (entries.length > 100) {
                logContainer.removeChild(entries[0]);
            }
        }
        
        // 格式化运行时间
        function formatUptime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);
            
            if (hours > 0) {
                return `${hours}h ${minutes}m ${secs}s`;
            } else if (minutes > 0) {
                return `${minutes}m ${secs}s`;
            } else {
                return `${secs}s`;
            }
        }
        
        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'monitoring': '监听中',
                'connecting': '连接中',
                'error': '错误',
                'disconnected': '已断开',
                'unknown': '未知'
            };
            return statusMap[status] || status;
        }
    </script>
</body>
</html>
