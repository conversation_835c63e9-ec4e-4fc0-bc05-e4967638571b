<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok直播间监控</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .monitor-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .monitor-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .monitor-card h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            margin: 10px 0;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
        }
        .status.online {
            background-color: #d4edda;
            color: #155724;
        }
        .status.offline {
            background-color: #f8d7da;
            color: #721c24;
        }
        .screenshot {
            width: 100%;
            margin-top: 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        .screenshot:hover {
            opacity: 0.9;
        }
        .controls {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }
        button {
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.7);
            z-index: 1000;
        }
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 90%;
            max-height: 90%;
        }
        .modal-content img {
            max-width: 100%;
            max-height: 100%;
        }
        .close {
            position: absolute;
            top: 10px;
            right: 10px;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }
        .event-log {
            margin-top: 15px;
            flex-grow: 1;
            overflow-y: auto;
            max-height: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .event-item {
            margin-bottom: 8px;
            padding: 5px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
            cursor: pointer;
        }
        .event-item:hover {
            background-color: #f8f9fa;
        }
        .event-details {
            display: none;
            margin-top: 5px;
            padding: 5px;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-size: 12px;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .event-details.show {
            display: block;
        }
        .event-time {
            color: #666;
            font-size: 12px;
            margin-right: 5px;
        }
        .event-message {
            word-break: break-all;
        }
        .event-type-1 { color: #ff6b6b; } /* 点赞 */
        .event-type-2 { color: #4dabf7; } /* 评论 */
        .event-type-3 { color: #ff922b; } /* 礼物 */
        .event-type-4 { color: #51cf66; } /* 粉丝团 */
        .event-type-5 { color: #339af0; } /* 关注 */
        .event-type-7 { color: #845ef7; } /* 用户进入 */
        .event-type-1000 { color: #868e96; } /* 房间状态 */
        .event-type-1001 { color: #868e96; } /* 房间初始化状态 */
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>TikTok</h1>
            <div>
                <input type="text" id="userId" placeholder="输入用户ID" />
                <button onclick="addMonitor()">添加监控</button>
            </div>
        </div>
        <div class="monitor-list" id="monitorList">
            <!-- 监控卡片将通过JavaScript动态添加 -->
        </div>
    </div>

    <div id="screenshotModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <div class="modal-content">
            <img id="modalImage" src="" alt="直播间截图">
        </div>
    </div>

    <script>
        // 存储所有监控的定时器
        const monitorTimers = new Map();
        let globalEventUpdateTimer = null;

        // 更新监控列表
        async function updateMonitors() {
            const response = await fetch('/api/monitors');
            const monitors = await response.json();
            const monitorList = document.getElementById('monitorList');
            monitorList.innerHTML = '';

            // 清除所有现有的定时器
            monitorTimers.forEach(timer => clearInterval(timer));
            monitorTimers.clear();

            monitors.forEach(monitor => {
                const card = document.createElement('div');
                card.className = 'monitor-card';
                card.innerHTML = `
                    <h3>${monitor.userId}</h3>
                    <div class="status ${monitor.isMonitoring ? 'online' : 'offline'}">
                        ${monitor.isMonitoring ? '监控中' : '已停止'}
                    </div>
                    <p>重试次数: ${monitor.retryCount}</p>
                    <p>最后刷新: ${new Date(monitor.lastRefreshTime).toLocaleString()}</p>
                    ${monitor.lastScreenshot ? `
                        <img class="screenshot" 
                             src="/screenshots/${monitor.lastScreenshot}" 
                             alt="直播间截图"
                             onclick="showScreenshot('/screenshots/${monitor.lastScreenshot}')">
                    ` : ''}
                    <div class="controls">
                        <button onclick="takeScreenshot('${monitor.userId}')">获取截图</button>
                        <button class="danger" onclick="removeMonitor('${monitor.userId}')">停止监控</button>
                    </div>
                    <div class="event-log" id="event-log-${monitor.userId}">
                        <!-- 事件日志将通过JavaScript动态添加 -->
                    </div>
                `;
                monitorList.appendChild(card);
                updateEventLog(monitor.userId);
            });

            // 设置全局事件更新定时器
            if (globalEventUpdateTimer) {
                clearInterval(globalEventUpdateTimer);
            }
            globalEventUpdateTimer = setInterval(() => {
                monitors.forEach(monitor => {
                    if (monitor.isMonitoring) {
                        updateEventLog(monitor.userId);
                    }
                });
            }, 1000);
        }

        // 更新事件日志
        async function updateEventLog(userId) {
            try {
                const response = await fetch(`/api/monitors/${userId}/events`);
                if (response.ok) {
                    const events = await response.json();
                    const eventLog = document.getElementById(`event-log-${userId}`);
                    if (eventLog) {
                        eventLog.innerHTML = events.map(event => `
                            <div class="event-item event-type-${event.event_type}" onclick="toggleEventDetails(this)">
                                <div class="event-header">
                                    <span class="event-time">${new Date(event.created_at).toLocaleString()}</span>
                                    <span class="event-message">${event.message}</span>
                                </div>
                                <div class="event-details">
                                    <pre>${JSON.stringify(event.payload, null, 2)}</pre>
                                </div>
                            </div>
                        `).join('');
                        // 自动滚动到底部
                        eventLog.scrollTop = eventLog.scrollHeight;
                    }
                }
            } catch (error) {
                console.error('更新事件日志失败:', error);
            }
        }

        // 切换事件详情显示
        function toggleEventDetails(element) {
            const details = element.querySelector('.event-details');
            details.classList.toggle('show');
        }

        // 添加监控
        async function addMonitor() {
            const userId = document.getElementById('userId').value.trim();
            if (!userId) {
                alert('请输入用户ID');
                return;
            }

            try {
                const response = await fetch('/api/monitors', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ userId })
                });

                if (response.ok) {
                    document.getElementById('userId').value = '';
                    updateMonitors();
                } else {
                    const error = await response.json();
                    alert(error.error);
                }
            } catch (error) {
                alert('添加监控失败');
            }
        }

        // 移除监控
        async function removeMonitor(userId) {
            try {
                const response = await fetch(`/api/monitors/${userId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    updateMonitors();
                } else {
                    const error = await response.json();
                    alert(error.error);
                }
            } catch (error) {
                alert('移除监控失败');
            }
        }

        // 获取截图
        async function takeScreenshot(userId) {
            try {
                const response = await fetch(`/api/monitors/${userId}/screenshot`);
                if (response.ok) {
                    updateMonitors();
                } else {
                    const error = await response.json();
                    alert(error.error);
                }
            } catch (error) {
                alert('获取截图失败');
            }
        }

        // 显示截图
        function showScreenshot(src) {
            const modal = document.getElementById('screenshotModal');
            const modalImg = document.getElementById('modalImage');
            modal.style.display = 'block';
            modalImg.src = src;
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('screenshotModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('screenshotModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // 初始化
        updateMonitors();

        // 设置状态变化监听
        const eventSource = new EventSource('/api/monitors/status-changed');
        eventSource.onmessage = function(event) {
            const data = JSON.parse(event.data);
            updateMonitors();
        };
    </script>
</body>
</html> 