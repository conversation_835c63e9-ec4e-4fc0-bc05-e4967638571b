// Modules to control application life and create native browser window

const {app, BrowserWindow} = require('electron');

const { startMonitorRoom} = require("./dist/tiktok");
var mainWindow;

// tiktok 需要添加代理
app.commandLine.appendSwitch('proxy-server', 'http://127.0.0.1:8080')

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1024,
    height: 768,
  })
  // mainWindow.webContents.session.clearCache(() => {
  //   console.log('Cache cleared');
  // });
  // startMonitorRoom(mainWindow, 'Win4717', douyinEvent, logCallback);
  // mainWindow.webContents.userAgent = 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36';
  startMonitorRoom(mainWindow, '@.1206844', tiktokEvent, logCallback);
  // mainWindow.webContents.openDevTools()
}

function processDebugUserBadges(combineBadgesList) {
  var ret_text = ""
  var i = 0
  combineBadgesList?.forEach(function (badge) {// repeated CombineBadge
    i+=1
    ret_text+="["+i+"]"+badge.sceneType+","+badge.str+","+badge?.text + ', color:' + badge.backgroundColorCode + ', icon:' + badge?.iconUrl
   });
  return ret_text
}
function fmtUserName(user) {
  var ret = ''
  if (user.userPrivilegeLevel !== undefined) {
    ret = user.userPrivilegeLevel;
  }
  if (user.fansclubLevel !== undefined) {
    ret += '|' + user.fansclubLevel;
  }
  // debug 用户标签信息
  // var combineList = processDebugUserBadges(user.combineBadges);
  // 处理所有类型的徽章
  var badgeText = '';
  if (user.combineBadges) {
    user.combineBadges.forEach(function(badge) {
      var displayText = badge.text || badge.str || '';
      // displayText = displayText + "("+badge.level+")";
      switch(badge.sceneType) {
        case 'BadgeSceneType_Unknown':
          badgeText += '[未知:' + displayText + ']';
          break;
        case 'BadgeSceneType_Admin':
          badgeText += '[管理员:' + displayText + ']';
          break;
        case 'BadgeSceneType_FirstRecharge':
          badgeText += '[首充:' + displayText + ']';
          break;
        case 'BadgeSceneType_Friends':
          badgeText += '[好友:' + displayText + ']';
          break;
        case 'BadgeSceneType_Subscriber':
          badgeText += '[订阅:' + displayText + ']';
          break;
        case 'BadgeSceneType_Activity':
          badgeText += '[活动:' + displayText + ']';
          break;
        case 'BadgeSceneType_Ranklist':
          badgeText += '[排行榜:' + displayText + ']';
          break;
        case 'BadgeSceneType_NewSubscriber':
          badgeText += '[新订阅:' + displayText + ']';
          break;
        case 'BadgeSceneType_UserGrade':
          badgeText += '[等级:' + displayText + ']';
          break;
        case 'BadgeSceneType_StateControlledMedia':
          badgeText += '[官方:' + displayText + ']';
          break;
        case 'BadgeSceneType_Fans':
          badgeText += '[粉丝:' + displayText + ']';
          break;
        case 'BadgeSceneType_LivePro':
          badgeText += '[直播达人:' + displayText + ']';
          break;
        case 'BadgeSceneType_Anchor':
          badgeText += '[主播:' + displayText + ']';
          break;
        default:
          badgeText += '[' + badge.sceneType + ':' + displayText + ']';
      }
    });
  }

  return badgeText+user.nickname + "(" + user.displayId + ")";// + "[" + JSON.stringify(user.combineBadges) + "]";
}
// tiktok 互动数据
function tiktokEvent(payload) {
  var payload_json = JSON.stringify(payload);
  // console.log(payload_json);

  var _events = JSON.parse(payload_json).payload;
  _events.forEach((_event) => {
    // console.log(JSON.stringify(_event));
    // console.log(_event.common.msgTypeStr);
    if (Number(_event['common'].msgType) === 1) {
      // 点赞数据
      // console.log(_event['common']);
      console.log(fmtUserName(_event.user) + ' 点赞x' +_event.likeNum + ',直播间总点赞数:'+_event.likeTotal);
    } else if (Number(_event['common'].msgType) === 2) {
      // 评论数据
      console.log(fmtUserName(_event.user) + ' 说['+_event.contentLanguage+']:' + _event.content);
    }  else if (Number(_event['common'].msgType) === 3) {
      // 礼物数据
      // console.log(_event);
      // console.log(fmtUserName(_event.user) + ' 送了:' + _event.giftName + ',combo:' + _event.combo
      //     + ',comboCount:' + _event.comboCount + ',groupCount:' + _event.groupCount
      //     +',repeatCount:'+_event.repeatCount+ ',repeatEnd:' + _event.repeatEnd
      //     + ',sendTime:' + _event.sendTime +',traceId:' + _event.traceId );
      if (_event.combo === true) {
        // combo 为 true, 最少会收到一次 _event.repeatEnd===0和一次 _event.repeatEnd===1 ，以End结束.
        // 需要记录该用户前一次的数量，因为可能会跳过,或者只记录最后一次的总数
        if (_event.repeatEnd === 1) {
          // 这里可以记录礼物总连击数: _event.groupCount*_event.comboCount
          if (_event.comboCount > 1) {
            console.log(fmtUserName(_event.user) + ' 一共送了:' + _event.giftName + ' x'+_event.groupCount*_event.comboCount + ' (连击结束)')
          }
        } else {
          // 只用来显示，因为礼物数据不一定是顺序增加的
          console.log(fmtUserName(_event.user) + ' 送了:' + _event.giftName + ' x'+_event.groupCount*_event.comboCount + (_event.comboCount > 1 ? (' (连击中)') : ''))
        }
      } else {
        // 当前消息礼物数: _event.groupCount
        console.log(fmtUserName(_event.user) + ' 送了:' + _event.giftName + ' x'+_event.groupCount + ' (连击x'+_event.groupCount*_event.comboCount+')')
      }

    }
    else if (Number(_event['common'].msgType) === 4) {
      // 粉丝团数据
      // console.log(_event);
      if (Number(_event.action) === 1) {
        // console.log(fmtUserName(_event.user) + ' 粉丝团等级升级到' + _event.user.fansclubLevel);
      } else if (Number(_event.action) === 2) {
        console.log(fmtUserName(_event.user) + ' 加入粉丝团');
      }
    }  else if (Number(_event['common'].msgType) === 5) {
      // 关注数据
      // console.log(_event);
      if (Number(_event.action) === 1) {
        console.log(fmtUserName(_event.user) + ' 关注, 总关注数' + _event.followCount);
      }
    } else if (Number(_event['common'].msgType) === 7) {
      // 用户进房
      // console.log(_event);
      console.log(fmtUserName(_event.user) + ' 来了,在线观众数:' + _event.memberCount);
    } else if (Number(_event['common'].msgType) === 1000) {
      // 直播间状态
      // console.log(_event);
      if (Number(_event.action) === 1) {
        console.log(_event.common.roomId + ' 主播离开一会儿');
      } else if (Number(_event.action) === 2) {
        console.log(_event.common.roomId + ' 主播回来啦');
      } else if (Number(_event.action) === 3) {
        console.log(_event.common.roomId + ' 直播已结束');
        mainWindow.close();
      } else {
        console.log(_event);
      }
    } else if (Number(_event['common'].msgType) === 1001) {
      // 开始监听时返回的直播间状态
      if (Number(_event.action) === 100) {
        console.log(_event.common.roomId + ' 正在直播');
      } else if (Number(_event.action) === 101) {
        console.log(_event.liveId + ' 未开播');
        mainWindow.close();
      } else if (Number(_event.action) === 102) {
        console.log('未找到直播间:' + _event.liveId);
        mainWindow.close();
      }
    } else {
      console.log(_event);
    }
  })
}

function logCallback(info) {
  console.log(info)
}

function createInputDialog(mainWindow) {
  let inputDialog = new BrowserWindow({
    width: 400,
    height: 200,
    parent: mainWindow,
    modal: true,
    show: false,
  });

  inputDialog.loadFile('inputDialog.html');

  inputDialog.once('ready-to-show', () => {
    inputDialog.show();
  });

  inputDialog.on('closed', () => {
    inputDialog = null;
  });
}
// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})


// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit()
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
