syntax = "proto3";
package tkMsg;


message InteractiveCommon {
  int64 msg_id = 1;// 消息id
  int64 room_id = 2;
  int32 msg_type = 3;// 互动数据类型，1-点赞、2-评论、3-礼物、4-粉丝团变更、5-关注变更、7-进房消息
  string msg_type_str = 4; // 互动数据类型，评论-live_comment、礼物-live_gift、点赞-live_like、粉丝团-live_fansclub、关注变更-live_follow、进房消息-live_enter_room
}

enum BadgeSceneType{
  BadgeSceneType_Unknown = 0;
  BadgeSceneType_Admin = 1;
  BadgeSceneType_FirstRecharge = 2;
  BadgeSceneType_Friends = 3;
  BadgeSceneType_Subscriber = 4;
  BadgeSceneType_Activity = 5;
  BadgeSceneType_Ranklist = 6;
  BadgeSceneType_NewSubscriber = 7;
  BadgeSceneType_UserGrade = 8;
  BadgeSceneType_StateControlledMedia = 9;
  BadgeSceneType_Fans = 10;
  BadgeSceneType_LivePro = 11;
  BadgeSceneType_Anchor = 12;
}
message CombineBadge{
  BadgeSceneType scene_type = 1;
  string text = 3;
  string str = 4;
  string icon_url = 5;
  string backgroundColorCode = 6;
  string level = 12;
}

message InteractiveUser{
  string nickname = 1;// 用户昵称
  string avatar_url = 2;  // 用户头像
  int64 user_privilege_level = 3; // 用户荣誉等级
  int64 fansclub_level = 4; // 用户的粉丝团等级
  string secUid = 5;  // 用户安全id
  string display_id = 6; // 用户id
  repeated CombineBadge combineBadges = 7;  // 用户类型
}

message to_user_info{
  string nickname = 1;// 用户昵称
  string avatar_url = 2;  // 用户头像
  string secUid = 3;  // 用户安全id
  string displayId = 4;
}

// 获取点赞数据
message LikeMessage {
  InteractiveCommon common = 1;
  InteractiveUser user = 2;

  int64 like_num = 3; // 点赞数
  int64 like_total = 4; // 点赞总数
}

// - 获取直播间评论
message ChatMessage{
  InteractiveCommon common = 1;
  InteractiveUser user = 2;
  string content = 3; // 评论内容
  int64 eventTime = 4;  // 评论的时间
  string contentLanguage = 5; // 评论的语言类型
  repeated EmoteWithIndex emotes = 6;// 评论中的表情贴纸
  UserIdentity user_identity = 7; // 用户的一些特征，比如是否关注主播，是否互相关注等..

  message EmoteWithIndex {
    int64 index = 1;  // 贴纸放置在 content 里的位置
    Emote emote = 2;
  }
  message Emote{
    string emote_id = 1;// 贴纸的id
    string image_url = 2;// 贴纸图片的url
    string image_uri = 3;// 贴纸图片的资源标识
  }
  message UserIdentity{
    bool is_gift_giver_of_anchor = 1;
    bool is_subscriber_of_anchor = 2;
    bool is_mutual_following_with_anchor = 3;
    bool is_follower_of_anchor = 4;
    bool is_moderator_of_anchor = 5;
    bool is_anchor = 6;
  }
}

// - 获取直播间礼物数据
message GiftMessage{
  InteractiveCommon common = 1;
  InteractiveUser user = 2;

  int64 gift_id = 3;  // 送出的礼物id
  bool combo = 4; // 礼物是否支持连点
  int64 comboCount = 5; // 连点次数
  int64 groupCount = 6; // 一组礼物个数
  int64 repeatCount = 7;// 送出的礼物数量，如果是连击，会叠加
  int32 repeatEnd = 8;  // 1 表示连击结束，如果为true,可以忽略这次的礼物数据。 （combo === true,这个值才有效）

  int64 diamondCount = 9;
  string gift_name = 10;
  string gift_url = 11;
  int64 sendTime = 12;  // 送出时间
  to_user_info to_user = 13; // 送给的用户,可能不存在
  string traceId = 14; // 追踪id
  // "gift_send_scene":"to_group_member",
}

// 粉丝团变更消息
message FansclubMessage{
  InteractiveCommon common = 1;
  InteractiveUser user = 2;

  int32 action = 3; // 粉丝团变更原因 1-升级、2-加团
}

// 关注消息
message SocialMessage{
  InteractiveCommon common = 1;
  InteractiveUser user = 2;

  int64 action = 3; // 用户关注数据  1 关注
  int64 follow_count = 4; // 当前主播的粉丝总数
}

// 排行消息
message UserSeqMessage{
  InteractiveCommon common = 1;
  repeated Contributor ranks = 2;
  int64 total = 3;
  int64 total_user = 4;
  int64 anonymous = 5;

  message Contributor{
    int64 score = 1;
    InteractiveUser user = 2;
    int64 rank = 3;
    int64 delta = 4;
  }
}

// 用户进房消息
message MemberMessage{
  InteractiveCommon common = 1;
  InteractiveUser user = 2;

  int64 memberCount = 3;  // 在线观众数
}

// 直播结束消息
message ControlMessage{
  InteractiveCommon common = 1;

  int64 action = 2; // 0 正在直播, 2 直播未开始, 3 直播结束
  string liveId = 3;  // liveId
}
