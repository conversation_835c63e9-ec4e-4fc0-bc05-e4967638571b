/**
 * 配置持久化演示
 * 展示客户端配置自动保存和恢复功能
 */

const { loadConfig } = require('../config/config-loader');
const ServerManager = require('../server/server-manager');
const { ClientApp } = require('../client/client-app');

class ConfigPersistenceDemo {
  constructor() {
    this.config = loadConfig();
    this.serverManager = null;
    this.clientApp = null;
  }

  /**
   * 运行演示
   */
  async runDemo() {
    console.log('🚀 配置持久化功能演示');
    console.log('='.repeat(50));
    
    try {
      // 启动服务端
      await this.startServer();
      
      // 等待服务端启动
      await this.sleep(2000);
      
      // 第一阶段：启动客户端并添加房间
      console.log('\n📍 阶段1: 启动客户端并添加房间');
      await this.startClientAndAddRooms();
      
      // 等待配置保存
      await this.sleep(3000);
      
      // 第二阶段：重启客户端，验证配置恢复
      console.log('\n📍 阶段2: 重启客户端，验证配置恢复');
      await this.restartClientAndVerifyRestore();
      
      // 等待验证完成
      await this.sleep(5000);
      
      console.log('\n✅ 演示完成！');
      console.log('💡 你可以通过以下方式查看结果:');
      console.log('   - 访问管理界面: http://localhost:3000/admin.html');
      console.log('   - 查看数据库统计: curl http://localhost:3000/api/stats');
      
    } catch (error) {
      console.error('❌ 演示失败:', error);
    }
  }

  /**
   * 启动服务端
   */
  async startServer() {
    console.log('🔧 启动服务端...');
    
    this.serverManager = new ServerManager({
      httpPort: this.config.server.http.port,
      wsPort: this.config.server.websocket.port,
      host: this.config.server.http.host,
      enableConfigPersistence: true,
      database: this.config.server.database,
    });
    
    // 监听配置恢复事件
    this.serverManager.wsServer.on('configRestored', ({ clientId, roomConfigs }) => {
      console.log(`✅ 配置已恢复 - 客户端: ${clientId}, 房间数: ${roomConfigs.length}`);
      roomConfigs.forEach(room => {
        console.log(`   📺 ${room.room_id} -> ${room.room_url}`);
      });
    });
    
    await this.serverManager.start();
    console.log('✅ 服务端启动成功');
  }

  /**
   * 启动客户端并添加房间
   */
  async startClientAndAddRooms() {
    console.log('🔧 启动客户端...');
    
    this.clientApp = new ClientApp({
      serverUrl: this.config.client.serverUrl,
      clientId: 'demo_client_001',
      maxRooms: this.config.client.maxRooms,
      headless: true, // 演示时使用无头模式
    });
    
    await this.clientApp.start();
    console.log('✅ 客户端启动成功');
    
    // 等待连接稳定
    await this.sleep(1000);
    
    // 添加测试房间
    const testRooms = [
      { roomId: 'demo_room_001', roomUrl: '@demo_user_1' },
      { roomId: 'demo_room_002', roomUrl: '@demo_user_2' },
      { roomId: 'demo_room_003', roomUrl: '@demo_user_3' },
    ];
    
    console.log('📺 添加测试房间...');
    for (const room of testRooms) {
      try {
        await this.clientApp.roomManager.addRoom(room.roomId, room.roomUrl, {
          autoReconnect: true,
          maxRetries: 3,
          demo: true,
        });
        console.log(`✅ 已添加房间: ${room.roomId} -> ${room.roomUrl}`);
        await this.sleep(500); // 避免添加过快
      } catch (error) {
        console.log(`⚠️  添加房间失败: ${room.roomId} - ${error.message}`);
      }
    }
    
    console.log('💾 配置已自动保存到数据库');
  }

  /**
   * 重启客户端并验证配置恢复
   */
  async restartClientAndVerifyRestore() {
    console.log('🔄 停止客户端...');
    
    // 停止客户端
    await this.clientApp.stop();
    console.log('✅ 客户端已停止');
    
    // 等待一段时间
    await this.sleep(2000);
    
    console.log('🔧 重新启动客户端...');
    
    // 重新创建客户端（使用相同的clientId）
    this.clientApp = new ClientApp({
      serverUrl: this.config.client.serverUrl,
      clientId: 'demo_client_001', // 使用相同的客户端ID
      maxRooms: this.config.client.maxRooms,
      headless: true,
    });
    
    // 监听配置恢复事件
    this.clientApp.wsClient.on('configRestore', ({ roomConfigs }) => {
      console.log(`🎉 收到配置恢复消息: ${roomConfigs.length} 个房间`);
      roomConfigs.forEach(room => {
        console.log(`   📺 ${room.room_id} -> ${room.room_url}`);
      });
    });
    
    await this.clientApp.start();
    console.log('✅ 客户端重新启动成功');
    console.log('🔍 等待配置自动恢复...');
  }

  /**
   * 清理资源
   */
  async cleanup() {
    console.log('\n🧹 清理资源...');
    
    if (this.clientApp) {
      await this.clientApp.stop();
    }
    
    if (this.serverManager) {
      await this.serverManager.stop();
    }
    
    console.log('✅ 清理完成');
  }

  /**
   * 睡眠函数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 交互式演示
async function interactiveDemo() {
  const demo = new ConfigPersistenceDemo();
  
  // 设置优雅退出
  const gracefulExit = async (signal) => {
    console.log(`\n👋 收到 ${signal} 信号，正在清理...`);
    await demo.cleanup();
    process.exit(0);
  };
  
  process.on('SIGINT', () => gracefulExit('SIGINT'));
  process.on('SIGTERM', () => gracefulExit('SIGTERM'));
  
  try {
    await demo.runDemo();
    
    // 保持运行，让用户可以查看结果
    console.log('\n⏳ 演示正在运行，按 Ctrl+C 退出');
    await new Promise(() => {}); // 永远等待
    
  } catch (error) {
    console.error('演示失败:', error);
    await demo.cleanup();
    process.exit(1);
  }
}

// 快速测试
async function quickTest() {
  console.log('⚡ 快速配置持久化测试');
  
  const demo = new ConfigPersistenceDemo();
  
  try {
    await demo.startServer();
    await demo.sleep(1000);
    
    await demo.startClientAndAddRooms();
    await demo.sleep(2000);
    
    await demo.restartClientAndVerifyRestore();
    await demo.sleep(3000);
    
    console.log('✅ 快速测试完成');
    
  } catch (error) {
    console.error('❌ 快速测试失败:', error);
  } finally {
    await demo.cleanup();
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const mode = args[0] || 'interactive';
  
  switch (mode) {
    case 'quick':
      await quickTest();
      break;
      
    case 'interactive':
    default:
      await interactiveDemo();
      break;
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { ConfigPersistenceDemo };
