/**
 * 基本使用示例
 * 演示如何使用TikTok Live Monitor系统
 */

const { loadConfig } = require('../config/config-loader');
const ServerManager = require('../server/server-manager');
const { ClientApp } = require('../client/client-app');

// 示例1: 启动服务端
async function startServerExample() {
  console.log('=== 启动服务端示例 ===');
  
  const config = loadConfig();
  const serverManager = new ServerManager({
    httpPort: config.server.http.port,
    wsPort: config.server.websocket.port,
    host: config.server.http.host,
  });
  
  // 监听事件
  serverManager.wsServer.on('liveEvent', ({ roomId, eventType, eventData }) => {
    console.log(`[事件] 房间 ${roomId}: ${eventType}`, eventData);
    
    // 在这里可以添加自定义处理逻辑
    if (eventType === 'gift') {
      console.log(`🎁 ${eventData.user.nickname} 送了 ${eventData.giftName} x${eventData.totalValue}`);
    } else if (eventType === 'comment') {
      console.log(`💬 ${eventData.user.nickname}: ${eventData.content}`);
    } else if (eventType === 'like') {
      console.log(`👍 ${eventData.user.nickname} 点赞 x${eventData.likeCount}`);
    }
  });
  
  await serverManager.start();
  console.log('服务端启动成功');
  
  return serverManager;
}

// 示例2: 启动客户端
async function startClientExample() {
  console.log('=== 启动客户端示例 ===');
  
  const config = loadConfig();
  const clientApp = new ClientApp({
    serverUrl: config.client.serverUrl,
    maxRooms: config.client.maxRooms,
  });
  
  await clientApp.start();
  console.log('客户端启动成功');
  
  return clientApp;
}

// 示例3: 通过API管理房间
async function roomManagementExample() {
  console.log('=== 房间管理示例 ===');
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    // 添加房间
    const addResponse = await fetch(`${baseUrl}/api/rooms`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        roomId: 'example_room_001',
        roomUrl: '@example_user',
        config: {
          autoReconnect: true,
          maxRetries: 3,
        },
      }),
    });
    
    const addResult = await addResponse.json();
    console.log('添加房间结果:', addResult);
    
    // 获取房间列表
    const listResponse = await fetch(`${baseUrl}/api/rooms`);
    const listResult = await listResponse.json();
    console.log('房间列表:', listResult.data);
    
    // 等待一段时间让房间运行
    console.log('等待10秒...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // 删除房间
    const deleteResponse = await fetch(`${baseUrl}/api/rooms/example_room_001`, {
      method: 'DELETE',
    });
    
    const deleteResult = await deleteResponse.json();
    console.log('删除房间结果:', deleteResult);
    
  } catch (error) {
    console.error('房间管理示例失败:', error);
  }
}

// 示例4: WebSocket客户端直接通信
async function websocketClientExample() {
  console.log('=== WebSocket客户端示例 ===');
  
  const WebSocket = require('ws');
  const { ClientMessage } = require('../protocol/websocket-protocol');
  
  const ws = new WebSocket('ws://localhost:8080');
  
  ws.on('open', () => {
    console.log('WebSocket连接已建立');
    
    // 发送客户端就绪消息
    const readyMessage = ClientMessage.clientReady({
      clientId: 'example_client',
      version: '1.0.0',
      capabilities: ['tiktok'],
    });
    
    ws.send(JSON.stringify(readyMessage.toJSON()));
    
    // 模拟发送一些事件
    setTimeout(() => {
      const eventMessage = ClientMessage.liveEvent('example_room', 'comment', {
        user: {
          userId: 'user_001',
          nickname: '示例用户',
          displayId: '12345',
        },
        content: '这是一条示例评论',
        language: 'zh',
      });
      
      ws.send(JSON.stringify(eventMessage.toJSON()));
    }, 2000);
  });
  
  ws.on('message', (data) => {
    const message = JSON.parse(data);
    console.log('收到服务器消息:', message);
  });
  
  ws.on('close', () => {
    console.log('WebSocket连接已关闭');
  });
  
  // 10秒后关闭连接
  setTimeout(() => {
    ws.close();
  }, 10000);
}

// 示例5: 自定义事件处理
class CustomEventHandler {
  constructor() {
    this.eventStats = {
      likes: 0,
      comments: 0,
      gifts: 0,
      follows: 0,
    };
  }
  
  handleEvent(roomId, eventType, eventData) {
    console.log(`[自定义处理] 房间 ${roomId}: ${eventType}`);
    
    switch (eventType) {
      case 'like':
        this.eventStats.likes += eventData.likeCount || 1;
        console.log(`累计点赞: ${this.eventStats.likes}`);
        break;
        
      case 'comment':
        this.eventStats.comments++;
        console.log(`评论内容: ${eventData.content}`);
        console.log(`累计评论: ${this.eventStats.comments}`);
        break;
        
      case 'gift':
        this.eventStats.gifts += eventData.totalValue || 1;
        console.log(`礼物: ${eventData.giftName} x${eventData.totalValue}`);
        console.log(`累计礼物价值: ${this.eventStats.gifts}`);
        break;
        
      case 'follow':
        this.eventStats.follows++;
        console.log(`新关注: ${eventData.user.nickname}`);
        console.log(`累计关注: ${this.eventStats.follows}`);
        break;
    }
  }
  
  getStats() {
    return { ...this.eventStats };
  }
}

// 主函数 - 运行所有示例
async function runExamples() {
  console.log('🚀 TikTok Live Monitor 使用示例');
  console.log('请确保已安装依赖: npm install');
  console.log('');
  
  const args = process.argv.slice(2);
  const example = args[0] || 'all';
  
  try {
    switch (example) {
      case 'server':
        await startServerExample();
        break;
        
      case 'client':
        await startClientExample();
        break;
        
      case 'api':
        await roomManagementExample();
        break;
        
      case 'websocket':
        await websocketClientExample();
        break;
        
      case 'custom':
        const handler = new CustomEventHandler();
        console.log('自定义事件处理器示例');
        handler.handleEvent('test_room', 'like', { likeCount: 5 });
        handler.handleEvent('test_room', 'comment', { content: '测试评论' });
        console.log('统计信息:', handler.getStats());
        break;
        
      case 'all':
      default:
        console.log('运行所有示例需要分别启动服务端和客户端');
        console.log('请使用以下命令:');
        console.log('  node examples/basic-usage.js server   # 启动服务端');
        console.log('  node examples/basic-usage.js client   # 启动客户端');
        console.log('  node examples/basic-usage.js api      # API管理示例');
        console.log('  node examples/basic-usage.js websocket # WebSocket示例');
        console.log('  node examples/basic-usage.js custom   # 自定义处理示例');
        break;
    }
    
  } catch (error) {
    console.error('示例运行失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runExamples();
}

module.exports = {
  startServerExample,
  startClientExample,
  roomManagementExample,
  websocketClientExample,
  CustomEventHandler,
};
