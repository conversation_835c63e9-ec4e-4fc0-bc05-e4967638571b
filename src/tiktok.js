// const { version } = require('../package.json');
//
// const pb_webcast = require("./bundle2.min");
// const zlib = require("zlib");

const pb_webcast = require("./release_tiktok_webcast.min");
// const pb_webcast = require("./auto_gen_webcast");
const {gunzip} = require("node:zlib");

/// @secureBegin

async function startMonitorPage(page, liveId, eventCallback, logCallback) {
    const client = await page.target().createCDPSession()

    await client.send('Network.enable')

    client.on('Network.webSocketCreated', ({requestId, url}) => {
        console.log('Network.webSocketCreated', requestId, url)
    })

    client.on('Network.webSocketClosed', ({requestId, timestamp}) => {
        console.log('Network.webSocketClosed', requestId, timestamp)
    })

    // client.on('Network.webSocketFrameSent', ({requestId, timestamp, response}) => {
    //     console.log('Network.webSocketFrameSent', requestId, timestamp, response.payloadData)
    // })

    client.on('Network.webSocketFrameReceived', ({requestId, timestamp, response}) => {
        // console.log('Network.webSocketFrameReceived', requestId, timestamp, response.payloadData)
        const PushFrameMsg = pb_webcast.PushFrame.decode(Buffer.from(response.payloadData, 'base64'));
        // console.log(PushFrameMsg)
        if (needUnzip(PushFrameMsg)) {
            // 是否需要解包:
            // console.log('needUnzip');
            gunzip(PushFrameMsg.payload, (err, uncompressedData) => {
                if (err) {
                    logCallback('解压缩失败:' + err);
                    return;
                }
                if (PushFrameMsg.payloadType === 'msg') {
                    try {
                        decodePayload(uncompressedData, liveId, eventCallback, logCallback);
                    } catch (e) {
                        logCallback('decodePayload ws error:' + e.message);
                    }
                } else if (PushFrameMsg.payloadType === 'hb') {
                    // ignore
                } else {
                    // console.log('unknown type:'+PushFrameMsg.payloadType);
                }
            });
        }
    })

    // page.on('request', request => {
    //     // console.log(request.url());
    // });

    page.on('response', async response => {
        if (response.url().includes("/webcast/room/enter/?")) {
            try {
                const buffer = await response.text();
                // console.log(response.url());
                // console.log('Buffer Type:', typeof buffer); // 应为 'object'（Buffer 类型）
                // console.log('Buffer Length:', buffer.length); // 打印字符串内容以便调试
                // console.log('Buffer Content:', buffer); // 打印字符串内容以便调试
                // Buffer Content: {"data":{"message":"room has finished","prompts":"This LIVE video has ended"},"extra":{"finished_perception_msg":"","now":1745380247372,"punish_info":null},"status_code":30003}
                // 直播结束
                // 异常情况需要抛出，重启
                if (buffer.length === 0) {
                    var _newMessage = pb_webcast.tkMsg.ControlMessage.fromObject({
                        common: pb_webcast.tkMsg.InteractiveCommon.fromObject({
                            msgId: 0,
                            msgType: 1001,
                            msgTypeStr: 'enter_live_status'
                        })
                    });
                    var _outPayload = {
                        "payload": [],
                        "timestamp": new Date().getTime(),
                    };
                    _newMessage.liveId = liveId;
                    _newMessage.action = 103;
                    _outPayload.payload.push(_newMessage);
                    eventCallback(_outPayload);
                } else {
                    var enter_room = JSON.parse(buffer);

                    var _newMessage = pb_webcast.tkMsg.ControlMessage.fromObject({
                        common: pb_webcast.tkMsg.InteractiveCommon.fromObject({
                            msgId: 0,
                            msgType: 1001,
                            msgTypeStr: 'enter_live_status'
                        })
                    });
                    var _outPayload = {
                        "payload": [],
                        "timestamp": new Date().getTime(),
                    };
                    _newMessage.liveId = liveId;
                    if (Number(enter_room.status_code) === 0) {
                        // 进入的直播间正在直播
                        _newMessage.action = 100;
                        _newMessage.common.roomId = enter_room.data.id;
                        _outPayload.payload.push(_newMessage);
                        eventCallback(_outPayload);
                    } else if (Number(enter_room.status_code) === 30003) {
                        // 进入未开播的直播间
                        _newMessage.action = 101;
                        _outPayload.payload.push(_newMessage);
                        eventCallback(_outPayload);
                    }
                }
            } catch (e) {

            }
        } else if (response.url().includes("/webcast/im/fetch/?")) {
            const buffer = await response.buffer();
            // Buffer<ArrayBuffer> Uint8Array
            // console.log("fetch");
            decodePayload(Uint8Array.from(buffer), liveId, eventCallback, logCallback);
            // console.log("fetch end");
        } else {
            // console.log(await response.text());
        }
    });

    page.goto("https://www.tiktok.com/"+liveId+"/live");
}

// tools
function needUnzip(t) {
    for (const e of Object.values(t.headers)) if ("compress_type" === e.key && "gzip" === e.value) return !0;
    return !1
}

function debug_log(_common, msg) {

    let logOn = true;
    let showMethodName = true;
    let showTime = true;
    let showRoomId = true;
    if (logOn/* && _common.isShowMsg*/) {
        var showMsg = ''
        if (showTime) {
            const date = new Date();
            const formattedDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds() + ':' + date.getMilliseconds();
            showMsg += '[' + formattedDate + ']';
        }
        if (showRoomId) {
            showMsg += '[' + _common.roomId + ']'
        }
        if (showMethodName) {
            showMsg += '[' + _common.method + ']'
        }
        showMsg += msg;
        console.log(showMsg);
    }
    if (!_common.isShowMsg) {
        if (_common.method === 'WebcastRoomUserSeqMessage'
            || _common.method === 'WebcastRoomRankMessage'
            || _common.method === 'WebcastRoomStatsMessage') {
            // 在线观众和排行榜信息不显示:忽略
            return;
        }
        console.log(_common);
        console.log('===============isShowMsg=false========================');
    }
}

function log(_common, msg) {
}

function parseFormatText(text) {
    var formatMsg = text.defaultPattern;// 里面有 {0:user} 之类的
    if (text.defaultPattern === undefined) {
        console.log('formatMsg === undefined unknown pieceType:' + JSON.stringify(text));
        return 'formatMsg === undefined';
    }
    text.pieces.forEach((piece) => {
        if (piece.type === 1) {// string
            formatMsg = formatMsg.replace(/\{\d+:string}/, piece.stringValue);
        } else if (piece.type === 11) {// user
            formatMsg = formatMsg.replace(/\{\d+:user}/, piece.userValue.user.nickname);
        } else if (piece.type === 15) {// image
            formatMsg = formatMsg.replace(/\{\d+:image}/, piece.imageValue.image.urlList[0]);
        } else {
            console.log('unknown pieceType:' + JSON.stringify(text));
        }
    });
    return formatMsg;
}

function self_debug(msg) {
    // console.log(msg)
}

function decodePayload(payload, roomId, eventCallback, logCallback) {
    if (typeof payload === 'string') {
        payload = Buffer.from(payload, 'base64');
    }

    var outPayload = {
        "payload": [],
        "timestamp": new Date().getTime(),
    };
    const response = pb_webcast.priv.Response.decode(payload)
    response.messages.forEach(function (msg) {
        var _decodeMessage;
        try {
            // self_debug(msg.method + ', ' + JSON.stringify(msg))
            if (msg.method === "WebcastLikeMessage") {
                // 点赞
                _decodeMessage = pb_webcast.webcast.LikeMessage.decode(msg.payload);
                // let formatMsg = parseFormatText(_decodeMessage.common.displayText);
                // 记录点赞个数
                // debug_log(_decodeMessage.common,  formatMsg + "x" + _decodeMessage.count);
                var _newMessage = pb_webcast.tkMsg.LikeMessage.create();
                _newMessage.common = pb_webcast.tkMsg.InteractiveCommon.create();
                _newMessage.common.roomId = _decodeMessage.common.roomId;
                _newMessage.common.msgId = _decodeMessage.common.msgId;
                _newMessage.common.msgType = 1;
                _newMessage.common.msgTypeStr = 'live_like';
                _newMessage.likeNum = _decodeMessage.count;
                _newMessage.likeTotal = _decodeMessage.total;
                if (_decodeMessage.user !== undefined && _decodeMessage.user !== null) {
                    processUserInfo(_decodeMessage, _newMessage);
                }
                outPayload.payload.push(_newMessage);
                // debug_log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            }
            // 评论数据
            else if (msg.method === "WebcastChatMessage") {
                /**
                 * 有 _decodeMessage.userIdentity.isGiftGiverOfAnchor true
                 * 有 _decodeMessage.userIdentity.isFollowerOfAnchor true
                 * contentLanguage en 语言类型
                 * */
                _decodeMessage = pb_webcast.webcast.ChatMessage.decode(msg.payload);
                log(_decodeMessage.common, _decodeMessage.user.nickname + ":" + _decodeMessage.content);
                var _newMessage = pb_webcast.tkMsg.ChatMessage.create();
                _newMessage.common = pb_webcast.tkMsg.InteractiveCommon.create();
                _newMessage.common.roomId = _decodeMessage.common.roomId;
                _newMessage.common.msgId = _decodeMessage.common.msgId;
                _newMessage.common.msgType = 2;
                _newMessage.common.msgTypeStr = 'live_comment';
                _newMessage.content = _decodeMessage.content;
                _newMessage.contentLanguage = _decodeMessage.contentLanguage;
                _newMessage.clientSendTime = _decodeMessage.common.clientSendTime;
                if (_decodeMessage.user !== undefined && _decodeMessage.user !== null) {
                    processUserInfo(_decodeMessage, _newMessage);
                }
                // {"common":{"method":"WebcastChatMessage","msgId":"7473040849883171601","roomId":"7472988463770487559","createTime":"1739952916443","isShowMsg":true,"foldType":"2","anchorFoldType":"2","fromIdc":"my2","roomMessageHeatLevel":"4","foldTypeForWeb":"2","anchorFoldTypeForWeb":"2","clientSendTime":"1739952916047"},"user":{"id":"6718893884770124801","nickname":"🐝はち🦕","avatarThumb":{"urlList":["https://p77-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/a4564b1a0254e4b6de44219a7e71670e~tplv-tiktokx-cropcenter:100:100.webp?dr=14579&nonce=26325&refresh_token=ed8d6987e765a4397a528d55dca22fa5&x-expires=1740124800&x-signature=5jftB3m0FUPliKHCJFy5kQQE8gM%3D&idc=my2&ps=13740610&shcp=fdd36af4&shp=a5d48078&t=4d5b0474","https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/a4564b1a0254e4b6de44219a7e71670e~tplv-tiktokx-cropcenter:100:100.webp?dr=14579&nonce=41643&refresh_token=0b886cb65b468710bf5d5273c50566db&x-expires=1740124800&x-signature=c56A1QUBLfx%2FJtJl639x%2BzX8jhA%3D&idc=my2&ps=13740610&shcp=fdd36af4&shp=a5d48078&t=4d5b0474","https://p77-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/a4564b1a0254e4b6de44219a7e71670e~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&nonce=6151&refresh_token=555608c36e69d35604eb0f1e0b590ed0&x-expires=1740124800&x-signature=U%2FOh5zcBCkJEX4Knu9Fm%2FWyO%2BOQ%3D&idc=my2&ps=13740610&shcp=fdd36af4&shp=a5d48078&t=4d5b0474"],"uri":"100x100/tos-alisg-avt-0068/a4564b1a0254e4b6de44219a7e71670e"},"avatarMedium":{"urlList":["https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/a4564b1a0254e4b6de44219a7e71670e~tplv-tiktokx-compress_quality_30:64:64.webp?lk3s=1e19605b&nonce=38347&refresh_token=0be2d4802ff0258f9e8d7570df69d6ab&x-expires=1739973600&x-signature=VpmjFYJ0jgP2r0Es6x91LfE9FSU%3D&shp=1e19605b&shcp=-","https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/a4564b1a0254e4b6de44219a7e71670e~tplv-tiktokx-compress_quality_30:64:64.jpeg?lk3s=1e19605b&nonce=53291&refresh_token=1b89e42b52ad53b0e2906fc863ce2ad6&x-expires=1739973600&x-signature=Q0VRk4CoOGKQW5pYVvxLD4Wi0c8%3D&shp=1e19605b&shcp=-"]},"badgeImageList":[{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-va/subs_badge_icon_101.png~tplv-obj.image","https://p19-webcast.tiktokcdn.com/webcast-va/subs_badge_icon_101.png~tplv-obj.image"],"uri":"webcast-va/subs_badge_icon_101.png","height":"16","width":"16","imageType":30}],"followInfo":{"followingCount":"325","followerCount":"83","followStatus":"1"},"payGrade":{},"userAttr":{},"displayId":"clarks8808","secUid":"MS4wLjABAAAARIInXh0BFoiBDNTs5l3gLJudJLFMfqdRJIQK4J8a8Lv0C7m56_B-hy0huOGAgOZK","badgeList":[{"displayType":"BADGE_DISPLAY_TYPE_IMAGE","priorityType":"BadgePriorityType_Relation","sceneType":"BadgeSceneType_Subscriber","position":"PositionLeft","privilegeLogExtra":{"dataVersion":"2","privilegeId":"7459985881393761032","privilegeVersion":"0","privilegeOrderId":"mock_sub_7459985881393761032","level":"1"},"image":{"displayType":"BADGE_DISPLAY_TYPE_IMAGE","image":{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-va/subs_badge_icon_101.png~tplv-obj.image","https://p19-webcast.tiktokcdn.com/webcast-va/subs_badge_icon_101.png~tplv-obj.image"],"uri":"webcast-va/subs_badge_icon_101.png","height":"16","width":"16","imageType":30}}},{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","priorityType":"BadgePriorityType_Platform","sceneType":"BadgeSceneType_UserGrade","position":"PositionLeft","OpenWebURL":"sslocal://webcast_lynxview_popup?use_spark=1&url=https%3A%2F%2Flf16-gecko-source.tiktokcdn.com%2Fobj%2Fbyte-gurd-source-sg%2Ftiktok%2Ffe%2Flive%2Ftiktok_live_revenue_user_level_main%2Fsrc%2Fpages%2Fprivilege%2Fpanel%2Ftemplate.js&hide_status_bar=0&hide_nav_bar=1&container_bg_color=00000000&height=90%25&bdhm_bid=tiktok_live_revenue_user_level_main&use_forest=1","display":true,"privilegeLogExtra":{"dataVersion":"2","privilegeId":"7138381747292542756","privilegeVersion":"0","privilegeOrderId":"mock_fix_width_transparent_7138381747292542756","level":"15"},"combine":{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","icon":{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-va/grade_badge_icon_lite_lv15_v2.png~tplv-obj.image","https://p19-webcast.tiktokcdn.com/webcast-va/grade_badge_icon_lite_lv15_v2.png~tplv-obj.image"],"uri":"webcast-va/grade_badge_icon_lite_lv15_v2.png","openWebUrl":"sslocal://webcast_lynxview_popup?use_spark=1&url=https%3A%2F%2Flf16-gecko-source.tiktokcdn.com%2Fobj%2Fbyte-gurd-source-sg%2Ftiktok%2Ffe%2Flive%2Ftiktok_live_revenue_user_level_main%2Fsrc%2Fpages%2Fprivilege%2Fpanel%2Ftemplate.js&hide_status_bar=0&hide_nav_bar=1&container_bg_color=00000000&height=90%25&bdhm_bid=tiktok_live_revenue_user_level_main&use_forest=1"},"str":"15","padding":{"useSpecific":true,"middlePadding":1,"badgeWidth":32},"fontStyle":{},"profileCardPanel":{"projectionConfig":{"icon":{}},"profileContent":{}},"background":{"image":{},"backgroundColorCode":"#B3477EFF"},"backgroundDarkMode":{"image":{},"backgroundColorCode":"#B3477EFF"},"publicScreenShowStyle":14,"personalCardShowStyle":15,"paddingNewFont":{"useSpecific":true,"middlePadding":1,"badgeWidth":32}}},{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","priorityType":"BadgePriorityType_Relation","sceneType":"BadgeSceneType_Fans","position":"PositionLeft","display":true,"privilegeLogExtra":{"dataVersion":"2","privilegeId":"7196929090442545925","privilegeVersion":"0","privilegeOrderId":"mock_fix_width_transparent_7196929090442545925","level":"20"},"combine":{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","icon":{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-va/fans_badge_icon_lv20_v0.png~tplv-obj.image","https://p19-webcast.tiktokcdn.com/webcast-va/fans_badge_icon_lv20_v0.png~tplv-obj.image"],"uri":"webcast-va/fans_badge_icon_lv20_v0.png"},"str":"Ⅲ","padding":{"useSpecific":true,"middlePadding":3,"badgeWidth":32},"fontStyle":{},"profileCardPanel":{"projectionConfig":{"icon":{}},"profileContent":{}},"background":{"image":{},"backgroundColorCode":"#B3D63D35"},"backgroundDarkMode":{"image":{},"backgroundColorCode":"#B3D63D35"},"publicScreenShowStyle":14,"personalCardShowStyle":15,"paddingNewFont":{"useSpecific":true,"middlePadding":2,"badgeWidth":32}}},{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","priorityType":"BadgePriorityType_Relation","sceneType":"BadgeSceneType_Subscriber","position":"PositionLeft","display":true,"privilegeLogExtra":{"dataVersion":"2","privilegeId":"7459985881393761032","privilegeVersion":"0","privilegeOrderId":"mock_sub_7459985881393761032","level":"1"},"combine":{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","icon":{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-va/subs_badge_icon_101.png~tplv-obj.image","https://p19-webcast.tiktokcdn.com/webcast-va/subs_badge_icon_101.png~tplv-obj.image"],"uri":"webcast-va/subs_badge_icon_101.png"},"str":"せいら沼","padding":{"useSpecific":true,"middlePadding":1,"leftPadding":2,"rightPadding":4,"horizontalPaddingRule":"HorizontalPaddingRuleUseLeftAndMiddleAndRight","verticalPaddingRule":"VerticalPaddingRuleUseTopAndBottom"},"background":{"image":{},"backgroundColorCode":"#99EF7300"},"publicScreenShowStyle":14,"personalCardShowStyle":14,"paddingNewFont":{"useSpecific":true,"middlePadding":1,"leftPadding":2,"rightPadding":4,"horizontalPaddingRule":"HorizontalPaddingRuleUseLeftAndMiddleAndRight","verticalPaddingRule":"VerticalPaddingRuleUseTopAndBottom"}}}]},"content":"素晴らしい体験させてもらいましたよ","emotes":[{"index":"17","emote":{"emoteId":"7324257075008146184","image":{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20~tplv-fyjuxuxag8-webp.webp","https://p19-webcast.tiktokcdn.com/webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20~tplv-fyjuxuxag8-webp.webp"],"uri":"webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20","avgColor":"#7C7CA3"},"packageId":"7238813739339090690"}},{"index":"18","emote":{"emoteId":"7324257075008146184","image":{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20~tplv-fyjuxuxag8-webp.webp","https://p19-webcast.tiktokcdn.com/webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20~tplv-fyjuxuxag8-webp.webp"],"uri":"webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20","avgColor":"#7A6D53"},"packageId":"7238813739339090690"}},{"index":"19","emote":{"emoteId":"7324257075008146184","image":{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20~tplv-fyjuxuxag8-webp.webp","https://p19-webcast.tiktokcdn.com/webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20~tplv-fyjuxuxag8-webp.webp"],"uri":"webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20","avgColor":"#666666"},"packageId":"7238813739339090690"}}],"contentLanguage":"ja","userIdentity":{"isSubscriberOfAnchor":true,"isFollowerOfAnchor":true},"commentQualityScores":[{"version":"user_type_rule","score":"300000"},{"version":"community-flagged","score":"9572829"},{"version":"commentator_id","score":"6718893884770124801"},{"version":"default","score":"1739952917129000"},{"version":"default_app","score":"10000"},{"version":"rankV3","score":"1739952920000099"},{"version":"ttp_rule_rerank","score":"1739952926363000"},{"version":"timestamp_desc","score":"98260047083637000"},{"version":"tikcast_community_comment_18866_v7_r655069","score":"1739952920158849"},{"version":"tikcast_community_comment_18866_v7_r655069_desc","score":"1739952929841150"},{"version":"idc_rule_rerank","score":"1739952916364000"},{"version":"v13_r712088","score":"1739952916364000"},{"version":"batch_rank_v1","score":"1739952940897409"},{"version":"batch_rank_v1_understanding","score":"1739952910897409"},{"version":"v20_r1002545","score":"1739952910602652"},{"version":"v20_r1002546","score":"1739952913156820"},{"version":"v20_r1203882","score":"1739952915616958"},{"version":"v20_r1203882_desc","score":"1739952914383041"},{"version":"v20_r1203882_understanding","score":"1739952914383041"}],"commentTag":["COMMENT_TAG_NORMAL"],"publicAreaMessageCommon":{"creatorSuccessInfo":{"tags":[{"tagType":19,"tagText":{"key":"ttlive_companionTool_tag_sentGifts30D","defaultPattern":"30日間にギフトを853個贈りました","defaultFormat":{},"pieces":[{"type":1,"format":{},"stringValue":"853"}]}}],"topic":{"topicActionType":"TOPIC_ACTION_TYPE_FOLLOW","topicText":{"key":"ttlive_negScreenCompanion_actionTag_memberLevelTag","defaultPattern":"ハイレベルなメンバー"},"topicTips":{"key":"ttlive_negScreenCompanion_actionTag_memberLevelTip","defaultPattern":"フォローバックして交流しましょう！"}}},"portraitInfo":{},"userInteractionInfo":{"likeCnt":"2439","commentCnt":"6"}},"screenTime":"1739952914008","signature":"65e235bfaef2edff05b93c7efff971ad","signatureVersion":"v1"}
                // 聊天消息里还有一个 emotes, 里面有 index
                if (Array.isArray(_decodeMessage.emotes) && _decodeMessage.emotes.length !== 0) {
                    // 存在
                    for (var i = 0; i < _decodeMessage.emotes.length; i++) {
                        // _decodeMessage.emotes[i].index; // 表示在 content 里的位置
                        _newMessage.emotes[i] = pb_webcast.tkMsg.ChatMessage.EmoteWithIndex.create();
                        _newMessage.emotes[i].index = _decodeMessage.emotes[i].index;
                        _newMessage.emotes[i].emote = pb_webcast.tkMsg.ChatMessage.Emote.create();
                        _newMessage.emotes[i].emote.emoteId = _decodeMessage.emotes[i].emote.emoteId;
                        _newMessage.emotes[i].emote.imageUrl = _decodeMessage.emotes[i].emote.image.urlList[0];
                        _newMessage.emotes[i].emote.imageUri = _decodeMessage.emotes[i].emote.image.uri;
                    }
                }
                if (_decodeMessage.userIdentity !== null && _decodeMessage.userIdentity !== undefined) {
                    _newMessage.userIdentity = pb_webcast.tkMsg.ChatMessage.UserIdentity.create();
                    _newMessage.userIdentity = _decodeMessage.userIdentity;
                }
                // self_debug(JSON.stringify(_decodeMessage));
                outPayload.payload.push(_newMessage);
                // debug_log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            } else if (msg.method === "WebcastGiftMessage") {
                _decodeMessage = pb_webcast.webcast.GiftMessage.decode(msg.payload);
                // debug_log(_decodeMessage.common, JSON.stringify(_decodeMessage));
                var _newMessage = pb_webcast.tkMsg.GiftMessage.create();
                _newMessage.common = pb_webcast.tkMsg.InteractiveCommon.create();
                _newMessage.common.roomId = _decodeMessage.common.roomId;
                _newMessage.common.msgId = _decodeMessage.common.msgId;
                _newMessage.common.msgType = 3;
                _newMessage.common.msgTypeStr = 'live_gift';
                _newMessage.giftId = _decodeMessage.giftId;
                _newMessage.combo = _decodeMessage.gift.combo;// 是否连击
                _newMessage.comboCount = _decodeMessage.comboCount;// 连击次数
                _newMessage.groupCount = _decodeMessage.groupCount;// 一组礼物个数
                _newMessage.repeatCount = _decodeMessage.repeatCount;// 送出的礼物数量
                _newMessage.repeatEnd = _decodeMessage.repeatEnd;// 1 表示连击结束，如果为true,可以忽略这次的礼物数据。 （combo === true,这个值才有效）
                _newMessage.diamondCount = _decodeMessage.gift.diamondCount;
                _newMessage.giftName = _decodeMessage.gift.name;
                if (_decodeMessage.gift.combo) {
                    // 如果礼物有 combo，触发第一个礼物之后，无论后面有没有发送，一定会收到一个 repeatEnd=1 的消息，而且traceId和上一条消息是一样的
                    // 1. 有时候 comboCount 会跳过一条，比如从1跳到3：
                    // [2025-1-23 18:29:3:655][7463048413089631030][WebcastGiftMessage]gift:1,1,人气票,品容💙,1,1,af1b1862054b11b5847768184335a08e,0,true
                    // [2025-1-23 18:29:4:775][7463048413089631030][WebcastGiftMessage]gift:3,3,人气票,品容💙,1,3,677e2b7e6643f982d4ee4d2d4b72999e,0,true
                    // [2025-1-23 18:29:8:179][7463048413089631030][WebcastGiftMessage]gift:3,3,人气票,品容💙,1,3,677e2b7e6643f982d4ee4d2d4b72999e,1,true

                    // 2. 有些礼物 combo 是 false,但是他 comboCount 也会递增,由于没有repeatEnd，所以不知道何时结束.(如果这种礼物也像1一样会跳过一条，这种无法处理，只有记录缓存)
                    // [2025-1-23 18:33:28:51][7463048413089631030][WebcastGiftMessage]gift:1,1,爆竹响新春,品容💙,1,1,7fadc23db9d4b1c9b366bb748619fcf4,0,false
                    // [2025-1-23 18:33:28:51][7463048413089631030][WebcastGiftMessage]gift:2,2,爆竹响新春,品容💙,1,2,f1148d7e858e96085f0f81503c288eca,0,false
                    // [2025-1-23 18:33:29:166][7463048413089631030][WebcastGiftMessage]gift:3,3,爆竹响新春,品容💙,1,3,721153b5d6c398ad20ee2dcc75512b4e,0,false
                    // [2025-1-23 18:33:33:706][7463048413089631030][WebcastGiftMessage]gift:4,4,爆竹响新春,品容💙,1,4,127aabd2bed0624bf913f25da503eac0,0,false
                }
                // 礼物有连击，会多次显示，最后一次 repeatEnd = 1 会重复.
                // debug_log(_decodeMessage.common, 'gift:'+_decodeMessage.totalCount + ','+_decodeMessage.repeatCount
                //     + ',' + _decodeMessage.gift.name + ',' + _decodeMessage.user.nickname + ',' + _decodeMessage.groupCount + ',' + _decodeMessage.comboCount
                //     +',' + _decodeMessage.traceId + ',' + _decodeMessage.repeatEnd + ',' + _decodeMessage.gift.combo + ',' + _decodeMessage.sendTime);

                if (_decodeMessage.gift.image !== undefined && _decodeMessage.gift.image !== null) {
                    _newMessage.giftUrl = _decodeMessage.gift.image.urlList[0];
                }
                _newMessage.sendTime = _decodeMessage.sendTime;
                _newMessage.traceId = _decodeMessage.traceId;
                if (_decodeMessage.user !== undefined && _decodeMessage.user !== null) {
                    processUserInfo(_decodeMessage, _newMessage);
                }
                if (_decodeMessage.toUser !== null) {
                    _newMessage.toUser = pb_webcast.tkMsg.to_user_info.create();
                    _newMessage.toUser.nickname = _decodeMessage.toUser.nickname;
                    _newMessage.toUser.secUid = _decodeMessage.toUser.secUid;
                    _newMessage.toUser.displayId = _decodeMessage.toUser.displayId;
                    if (_decodeMessage.toUser.avatarThumb !== undefined && _decodeMessage.toUser.avatarThumb !== null
                        && _decodeMessage.toUser.avatarThumb.urlList !== undefined && _decodeMessage.toUser.avatarThumb.urlList !== null) {
                        _newMessage.toUser.avatarUrl = _decodeMessage.toUser.avatarThumb.urlList[0];
                    }
                }
                outPayload.payload.push(_newMessage);
                // self_debug(JSON.stringify(_decodeMessage));
            } else if (msg.method === 'WebcastFansclubMessage') {
                // 没有displayText
                // [2025-1-17 15:0:22:32][7460736793282562857][WebcastFansclubMessage]{"common":{"method":"WebcastFansclubMessage","msgId":"7460775750880096041","roomId":"7460736793282562857","isShowMsg":true},"action":7,"user":{"id":"94528882049","shortId":"796497705","nickname":"月baby🌙","gender":2,"signature":"被爱好似有靠山❤️","avatarThumb":{"urlList":["https://p3.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=306,"https://p11.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********","https://p26.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********"],"uri":"100x100/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100"},"avatarMedium":{"urlList":["https://p11.douyinpic.com/aweme/720x720/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********","https://p3.douyinpic.com/aweme/720x720/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********","https://p26.douyinpic.com/aweme/720x720/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********"],"uri":"720x720/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100"},"avatarLarge":{"urlList":["https://p11.douyinpic.com/aweme/1080x1080/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********","https://p3.douyinpic.com/aweme/1080x1080/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********","https://p26.douyinpic.com/aweme/1080x1080/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********"],"uri":"1080x1080/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100"},"verified":true,"city":"衡水","status":1,"modifyTime":"**********","areQrcodeUri":"7b7d001f0c5cc7d20e8a","badgeImageList":[{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image"],"uri":"webcast/new_user_grade_level_v1_35.png","height":"16","width":"32","imageType":1,"content":{"level":"35","alternativeText":"荣誉等级35级勋章"}},{"urlList":["https://p3-webcast.douyinpic.com/img//fansclub_level_v6_15.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/fansclub_level_v6_15.png~tplv-obj.image"],"uri":"webcast/fansclub_level_v6_15.png","imageType":7,"content":{"name":"迪士尼","fontColor":"#FFF","level":"15","alternativeText":"迪士尼粉丝团勋章"}}],"followInfo":{"followingCount":"113","followerCount":"19639","followerCountStr":"2万","followingCountStr":"113"},"payGrade":{"level":"35","thisGradeMinDiamond":"230000","taxDiamond":"300000","newImIconWithLevel":{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image"],"uri":"webcast/new_user_grade_level_v1_35.png","height":"16","width":"32","imageType":1},"newLiveIcon":{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/aweme_pay_grade_2x_35_39.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/aweme_pay_grade_2x_35_39.png~tplv-obj.image"],"uri":"webcast/aweme_pay_grade_2x_35_39.png","height":"12","width":"12","imageType":1}},"fansClub":{"data":{"clubName":"迪士尼","level":15,"usersClubStatus":1,"badge":{"icons":{"2":{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/fansclub_level_v6_15.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/fansclub_level_v6_15.png~tplv-obj.image"],"uri":"webcast/fansclub_level_v6_15.png"},"4":{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/star_guard_advanced_badge_15_xmp.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/star_guard_advanced_badge_15_xmp.png~tplv-obj.image"],"uri":"webcast/star_guard_advanced_badge_15_xmp.png","flexSettingList":{"settingList":["0","0","62","18"]},"textSettingList":{"settingList":["0","0","62","18"]}}},"title":"迪士尼"},"anchorId":"65305021"}},"userAttr":{},"linkMicStats":1,"displayId":"748983y.","withCommercePermission":true,"withFusionShopEntry":true,"secUid":"MS4wLjABAAAA_Rd-EmKuOLmAZ6bOU0AXgAEAJLGW6fgDNqSiMHYrRyU","authorizationInfo":3,"adversaryAuthorizationInfo":1,"badgeImageListV2":[{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image"],"uri":"webcast/new_user_grade_level_v1_35.png","height":"16","width":"32","imageType":1,"content":{"level":"35","alternativeText":"荣誉等级35级勋章"}},{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/fansclub_level_v6_15lv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/fansclub_level_v6_15.png~tplv-obj.image"],"uri":"webcast/fansclub_level_v6_15.png","imageType":7,"content":{"name":"迪士尼","fontColor":"#FFFFFF","level":"15","alternaeText":"迪士尼粉丝团勋章"}},{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/star_guard_advanced_badge_15_xmp.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/star_guard_advanced_badge_15_xmp.png~tplv-e"],"uri":"webcast/star_guard_advanced_badge_15_xmp.png","imageType":51,"content":{"name":"迪士尼","fontColor":"#FFFFFF","level":"15","alternativeText":"迪士尼粉丝团勋章"},"flexSettingList":{"settingList":["0","0","62","18"]},"tist":{"settingList":["0","0","62","18"]}}],"locationCity":"衡水","fansGroupInfo":{"listFansGroupUrl":"sslocal://webcast_lynxview?height=754&radius=8&gravity=bottom&type=popup&animation_type=present&url=https%3A%2F%2Flf-webcast-srcecdn-tos.bytegecko.com%2Fobj%2Fbyte-gurd-source%2Fwebcast%2Fmono%2Flynx%2Fdouyin_lynx_fansclub%2Ftemplate%2Fpages%2Ffansclub%2Ffans_group%2Fuser%2Ftemplate.js&load_taro=0&fallback_url=sslocal%3A%2F%2Fwebcast_webview%3Furl%3Dhttps%253A%252F%252Flf-webcast-sourcecdn-tos.bytegecko.com%252Fobj%252Fbyte-gurd-source%252Fwebcast%252Fmono%252Flynx%252Fdouyin_lynx_fansclub%252Ftemplate%252Fpages%252Ffansclub%252Ffans_group%252Fuser%252Findex.html%26type%3Dpopup%26gravity%3Dbottom%26height%3D754%26radius%3D8%26load_taro%3D0"},"mysteryMan":1,"jAccreditInfo":{},"subscribe":{},"webcastUid":"MS4wLjPZnV_HMPcONy7lczqH14YEx-eKLO6wCXX_JC1OEYTAoHu74hvwgavYKpEJU-naW78","publicAreaBadgeInfo":{"badgeInfoMap":{"\u0006\u0000\u0000\u0000\u0000\u0000\u0000\u0000":{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image"],"uri":"webcast/new_user_grade_level_v1_35.png","height":"16","width":"32","imageType":1,"content":{"level":"35","alternativeText":"荣誉等级35级勋章"}},"\f\u0000\u0000\u0000\u0000\u0000\u0000:{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/star_guard_advanced_badge_15_xmp.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/star_guard_advanced_badge_15_xmp.png~tplv-obj.image"],"uri":"webcast/star_guard_advanced_badge_15_xmp.png","imageType":51,"content":{"name":"迪士尼","fontColor":"#FFFFFF","level":"15","alternativeText":"迪士尼粉丝团勋章"},"flexSettingList":{"settingList":["0","0","62","18"]},"textSettingList":{"s:["0","0","62","18"]}}},"badgeList":["6","12"]},"idStr":"94528882049"},"publicAreaCommon":{"individualPriority":"30","imAction":3}}
                // "恭喜 {0:user} 加入粉丝团成为第{1:string}名"{2:string}"
                _decodeMessage = pb_webcast.webcast.FansclubMessage.decode(msg.payload);
                if (_decodeMessage.action === 2 || _decodeMessage.action === 1) {
                    // 新加入, 升级
                } else {
                    // TODO: test
                    // logCallback(_decodeMessage.content + ',' + _decodeMessage.action);
                }
                var _newMessage = pb_webcast.tkMsg.FansclubMessage.create();
                _newMessage.common = pb_webcast.tkMsg.InteractiveCommon.create();
                _newMessage.common.roomId = _decodeMessage.common.roomId;
                _newMessage.common.msgId = _decodeMessage.common.msgId;
                _newMessage.common.msgType = 4;
                _newMessage.common.msgTypeStr = 'live_fansclub';
                _newMessage.action = _decodeMessage.action;
                if (_decodeMessage.user !== undefined && _decodeMessage.user !== null) {
                    processUserInfo(_decodeMessage, _newMessage);
                }
                outPayload.payload.push(_newMessage);
            } else if (msg.method === "WebcastSocialMessage") {
                _decodeMessage = pb_webcast.webcast.SocialMessage.decode(msg.payload);
                // 关注主播 type == 1
                // 分享直播 type == 3
                if (Number(_decodeMessage.action) === 1) {
                    var _newMessage = pb_webcast.tkMsg.SocialMessage.create();
                    _newMessage.common = pb_webcast.tkMsg.InteractiveCommon.create();
                    _newMessage.common.roomId = _decodeMessage.common.roomId;
                    _newMessage.common.msgId = _decodeMessage.common.msgId;
                    _newMessage.common.msgType = 5;
                    _newMessage.common.msgTypeStr = 'live_follow';
                    _newMessage.action = _decodeMessage.action;
                    _newMessage.followCount = _decodeMessage.followCount;
                    if (_decodeMessage.user !== undefined && _decodeMessage.user !== null) {
                        processUserInfo(_decodeMessage, _newMessage);
                    }
                    outPayload.payload.push(_newMessage);
                }
            } else if (msg.method === "WebcastMemberMessage") {
                _decodeMessage = pb_webcast.webcast.MemberMessage.decode(msg.payload);
                if (Number(_decodeMessage.action) === 1) {
                    var _newMessage = pb_webcast.tkMsg.MemberMessage.create();
                    _newMessage.common = pb_webcast.tkMsg.InteractiveCommon.create();
                    _newMessage.common.roomId = _decodeMessage.common.roomId;
                    _newMessage.common.msgId = _decodeMessage.common.msgId;
                    _newMessage.common.msgType = 7;
                    _newMessage.common.msgTypeStr = 'live_enter_room';
                    if (_decodeMessage.user !== undefined && _decodeMessage.user !== null) {
                        processUserInfo(_decodeMessage, _newMessage);
                    }
                    _newMessage.memberCount = _decodeMessage.memberCount;
                    outPayload.payload.push(_newMessage);
                } else {
                    // {"common":{"method":"WebcastMemberMessage","msgId":"7469733849273387794","roomId":"7469711765472103176","createTime":"1739182940791","isShowMsg":true},"operator":{"id":"7021861793069515777","nickname":"ปัด🅰️Ⓜ️คุณซิน มาเชียร์","displayId":"pud9942","secUid":"MS4wLjABAAAANvzDNxqVWngL9WgAoW80PTGZbBULTdBu45qUAQ-GQGDCODP5y6TDG4BN4fOaPRBf"},"action":"26"}
                    // action: 26, 27 之后会有 WebcastRoomPinMessage 消息
                    // 26 设置 pin 消息
                    // 27 应该是取消 pin 消息
                    // todo: debug
                    self_debug(JSON.stringify(_decodeMessage));
                }
            } else if (msg.method === 'WebcastControlMessage') {
                _decodeMessage = pb_webcast.webcast.ControlMessage.decode(msg.payload);
                // self_debug(JSON.stringify(_decodeMessage));
                var _newMessage = pb_webcast.tkMsg.ControlMessage.fromObject({
                    common: pb_webcast.tkMsg.InteractiveCommon.fromObject({
                        roomId: _decodeMessage.common.roomId,
                        msgId: _decodeMessage.common.msgId,
                        msgType: 1000,
                        msgTypeStr: 'live_status',
                    }),
                    action: _decodeMessage.action
                });
                outPayload.payload.push(_newMessage);
            } else if (msg.method === 'WebcastRoomUserSeqMessage') {
                _decodeMessage = pb_webcast.webcast.RoomUserSeqMessage.decode(msg.payload);
                var showMsg = 'ranks:';
                _decodeMessage.ranks.forEach((_rank) => {
                    showMsg += '[' + _rank.rank + ']' + _rank.user.nickname + ','
                });
                showMsg += '在线观众:' + _decodeMessage.total + ',TotalUser:' + _decodeMessage.totalUser;
                // self_debug(showMsg);
                // self_debug(JSON.stringify(_decodeMessage));
            }
                // else if (msg.method === 'WebcastUnauthorizedMemberMessage') {
                //     // {"common":{"method":"WebcastUnauthorizedMemberMessage","msgId":"7469979692280728362","roomId":"7469929484154604293","createTime":"1739240183688","isShowMsg":true,"foldType":"1","anchorFoldType":"1"},"action":1,"nickNamePrefix":{"key":"web_nonlogin_im_1","defaultPattern":"Viewer%s","defaultFormat":{"color":"#ffffffff","weight":400}},"nickName":"256052","enterText":{"key":"live_room_enter_toast","defaultPattern":"{0:user} joined","defaultFormat":{"color":"#ffffffff","weight":400}}}
                //     // 未登录的用户进入直播间
                //     _decodeMessage = pb_webcast.webcast.UnauthorizedMemberMessage.decode(msg.payload);
                //     self_debug(JSON.stringify(_decodeMessage));
            // }
            else if (msg.method === 'WebcastRoomPinMessage') {
                // {"common":{"method":"WebcastRoomPinMessage","msgId":"7471131345818438443","roomId":"7471092186025970474","createTime":"1739508338046","isShowMsg":true},"chatMessage":{"common":{"method":"WebcastChatMessage","msgId":"7471131367271648046","roomId":"7471092186025970474","createTime":"1739508329749","isShowMsg":true,"foldType":"2","anchorFoldType":"2","fromIdc":"useast5","anchorPriorityScore":"1000","roomMessageHeatLevel":"4","foldTypeForWeb":"2","anchorFoldTypeForWeb":"2","clientSendTime":"1739508329263"},"user":{"id":"7111486241325499434","nickname":"Blessing's","avatarThumb":{"urlList":["https://p16-sign.tiktokcdn-us.com/tos-useast5-avt-0068-tx/40a7532208eb9c388b544e5f98f93512~c5_100x100.webp?lk3s=a5d48078&nonce=44791&refresh_token=67e34261476f9153948c38335a84a872&x-expires=1739678400&x-signature=EfrT0RoPnJgBTNu0ZtuJfsfZrpE%3D&shp=a5d48078&shcp=fdd36af4","https://p19-sign.tiktokcdn-us.com/tos-useast5-avt-0068-tx/40a7532208eb9c388b544e5f98f93512~c5_100x100.webp?lk3s=a5d48078&nonce=25340&refresh_token=a6459a8a29f33f899f576524ce49745a&x-expires=1739678400&x-signature=Piy1FOLLkagBjFfXUwbgm1hvavE%3D&shp=a5d48078&shcp=fdd36af4","https://p16-sign.tiktokcdn-us.com/tos-useast5-avt-0068-tx/40a7532208eb9c388b544e5f98f93512~c5_100x100.jpeg?lk3s=a5d48078&nonce=95897&refresh_token=f8323187053034160ca6eaaccaea01c3&x-expires=1739678400&x-signature=kiINPpJKIjoOva6KzWF3W86XyxQ%3D&shp=a5d48078&shcp=fdd36af4"],"uri":"100x100/tos-useast5-avt-0068-tx/40a7532208eb9c388b544e5f98f93512"},"badgeImageList":[{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-va/subs_badge_Lv1.png~tplv-obj.image","https://p19-webcast.tiktokcdn.com/webcast-va/subs_badge_Lv1.png~tplv-obj.image"],"uri":"webcast-va/subs_badge_Lv1.png","height":"16","width":"16","imageType":30}],"followInfo":{"followingCount":"10000","followerCount":"7691","followStatus":"2"},"payGrade":{},"userAttr":{},"displayId":"2merri1111","secUid":"MS4wLjABAAAA_bmOfMAZmiR3CvYTPMRMe8L37xMKY9_dlg3gwo8ronLc8ogqqOV1NVG8EFJRIKWZ","badgeList":[{"displayType":"BADGE_DISPLAY_TYPE_IMAGE","priorityType":"BadgePriorityType_Relation","sceneType":"BadgeSceneType_Subscriber","position":"PositionLeft","privilegeLogExtra":{"dataVersion":"2","privilegeId":"7449250932443581226","privilegeVersion":"0","privilegeOrderId":"mock_sub_7449250932443581226","level":"0"}},{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","priorityType":"BadgePriorityType_Platform","sceneType":"BadgeSceneType_UserGrade","position":"PositionLeft","OpenWebURL":"sslocal://webcast_lynxview_popup?use_spark=1&url=https%3A%2F%2Flf16-gecko-source.tiktokcdn.com%2Fobj%2Fbyte-gurd-source-sg%2Ftiktok%2Ffe%2Flive%2Ftiktok_live_revenue_user_level_main%2Fsrc%2Fpages%2Fprivilege%2Fpanel%2Ftemplate.js&hide_status_bar=0&hide_nav_bar=1&container_bg_color=00000000&height=90%25&bdhm_bid=tiktok_live_revenue_user_level_main&use_forest=1","display":true,"privilegeLogExtra":{"dataVersion":"2","privilegeId":"7138381747292526372","privilegeVersion":"0","privilegeOrderId":"mock_fix_width_transparent_7138381747292526372","level":"14"}},{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","priorityType":"BadgePriorityType_Relation","sceneType":"BadgeSceneType_Fans","position":"PositionLeft","display":true,"privilegeLogExtra":{"dataVersion":"2","privilegeId":"7196929090442513157","privilegeVersion":"0","privilegeOrderId":"mock_fix_width_transparent_7196929090442513157","level":"5"}},{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","priorityType":"BadgePriorityType_Relation","sceneType":"BadgeSceneType_Subscriber","position":"PositionLeft","display":true,"privilegeLogExtra":{"dataVersion":"2","privilegeId":"7449250932443581226","privilegeVersion":"0","privilegeOrderId":"mock_sub_7449250932443581226","level":"0"}}]},"content":"Rest.   Up.   Peeps 🐥","contentLanguage":"en","userIdentity":{"isGiftGiverOfAnchor":true,"isSubscriberOfAnchor":true,"isMutualFollowingWithAnchor":true,"isFollowerOfAnchor":true},"commentQualityScores":[{"version":"user_type_rule","score":"300000"},{"version":"community-flagged","score":"5537109"},{"version":"commentator_id","score":"7111486241325499434"},{"version":"default","score":"1739508329702000"},{"version":"default_app","score":"10000"},{"version":"rankV3","score":"1739508329705000"},{"version":"ttp_rule_rerank","score":"1739508329705000"},{"version":"timestamp_desc","score":"98260491670295000"},{"version":"tikcast_community_comment_18866_v7_r655069","score":"1739508329705000"},{"version":"tikcast_community_comment_18866_v7_r655069_desc","score":"1739508329705000"},{"version":"idc_rule_rerank","score":"1739508329705000"},{"version":"v13_r712088","score":"1739508329705000"},{"version":"v12_r702075","score":"1739508329705000"}],"commentTag":["COMMENT_TAG_NORMAL"],"publicAreaMessageCommon":{"portraitInfo":{},"userInteractionInfo":{"likeCnt":"7731","commentCnt":"119","shareCnt":"8"}},"screenTime":"1739508323312","signature":"693780bbf3b4ae97f3a1255a05da53cc","signatureVersion":"v1"},"method":"WebcastChatMessage","pinTime":"1739508338047","operator":{"id":"6725502374993773573","userAttr":{}},"action":"Pin","displayDuration":"60","pinMsgId":"7471131345818438443"}
                _decodeMessage = pb_webcast.webcast.RoomPinMessage.decode(msg.payload);
                //self_debug(JSON.stringify(_decodeMessage));
            }
                // else if (msg.method === 'RoomMessage') {
                //     _decodeMessage = pb_webcast.webcast.RoomMessage.decode(msg.payload);
                //     if (_decodeMessage.isWelcome) {
                //         // self_debug(JSON.stringify(_decodeMessage));
                //     }
            // }


            else {
                self_debug(msg.method);
            }
        } catch (e) {
            logCallback(_decodeMessage.common.method+' message error:' + e.message + '\n' + JSON.stringify(_decodeMessage));
        }
    })
    if (outPayload.payload.length > 0) {
        eventCallback(outPayload);
    }
}

/// @secureEnd

module.exports = {
    startMonitorPage: startMonitorPage,
};
// export default startMonitorRoomMsg;

/**
 * 处理用户徽章列表
 * @param {Array} badgeList - 用户徽章列表
 * @param {Object} _newMessage - 消息对象
 */
function processDebugUserBadges(badgeList, _newMessage) {
    badgeList.forEach(function (badge) {// repeated CombineBadge
        if (badge.displayType === pb_webcast.webcast.BadgeDisplayType.BADGE_DISPLAY_TYPE_COMBINE) {
            if (badge.sceneType === pb_webcast.webcast.BadgeSceneType.BadgeSceneType_UserGrade) {
                // self_debug(badge.combine.str + ', color:' + badge.combine.background.backgroundColorCode + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
            } else if (badge.sceneType === pb_webcast.webcast.BadgeSceneType.BadgeSceneType_Fans) {
                // self_debug(badge.combine.str + ', color:' + badge.combine.background.backgroundColorCode + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
            }

            if (badge.sceneType === pb_webcast.webcast.BadgeSceneType.BadgeSceneType_UserGrade) {
                // self_debug(badge.combine.displayType+","+badge.combine.str + ', color:' + badge.combine.background.backgroundColorCode + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
            } else if (badge.sceneType === pb_webcast.webcast.BadgeSceneType.BadgeSceneType_Fans) {
                // self_debug(badge.combine.displayType+","+badge.combine.str + ', color:' + badge.combine.background.backgroundColorCode + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
            } else if (badge.sceneType === pb_webcast.webcast.BadgeSceneType.BadgeSceneType_FirstRecharge) {
                // New gifter
                self_debug(badge.combine.str+","+(badge.combine && badge.combine.text ? badge.combine.text.defaultPattern : '') + ', color:' + (badge.combine && badge.combine.background ? badge.combine.background.backgroundColorCode : '') + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
            } else if (badge.sceneType === pb_webcast.webcast.BadgeSceneType.BadgeSceneType_Ranklist) {
                // ranklist
                self_debug(badge.combine.str+","+(badge.combine && badge.combine.text ? badge.combine.text.defaultPattern : '') + ', color:' + (badge.combine && badge.combine.background ? badge.combine.background.backgroundColorCode : '') + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
            } else if (badge.sceneType === pb_webcast.webcast.BadgeSceneType.BadgeSceneType_Subscriber) {
                // Subscriber
                self_debug(badge.combine.str+","+(badge.combine && badge.combine.text ? badge.combine.text.defaultPattern : '') + ', color:' + (badge.combine && badge.combine.background ? badge.combine.background.backgroundColorCode : '') + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
            } else if (badge.sceneType === pb_webcast.webcast.BadgeSceneType.BadgeSceneType_Admin) {
                // Moderator
                self_debug(badge.combine.str+","+(badge.combine && badge.combine.text ? badge.combine.text.defaultPattern : '') + ', color:' + (badge.combine && badge.combine.background ? badge.combine.background.backgroundColorCode : '') + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
            } else {
                self_debug('else sceneType:'+JSON.stringify(badge));
            }
        }
    });
}

/**
 * 处理用户信息
 * @param {Object} _decodeMessage - 解码后的消息对象
 * @param {Object} _newMessage - 新消息对象
 */
function processUserInfo(_decodeMessage, _newMessage) {
    _newMessage.user = pb_webcast.tkMsg.InteractiveUser.create();
    _newMessage.user.nickname = _decodeMessage.user.nickname;
    _newMessage.user.secUid = _decodeMessage.user.secUid;
    _newMessage.user.displayId = _decodeMessage.user.displayId;
    if (_decodeMessage.user.avatarThumb !== null && _decodeMessage.user.avatarThumb.urlList !== null && _decodeMessage.user.avatarThumb.urlList !== undefined) {
        _newMessage.user.avatarUrl = _decodeMessage.user.avatarThumb.urlList[0];
    }
    // if (_decodeMessage.user.fansClub !== null && _decodeMessage.user.fansClub.data !== null && _decodeMessage.user.fansClub.data !== undefined) {
    //     _newMessage.user.fansclubLevel = _decodeMessage.user.fansClub.data.level;
    // }
    if (_decodeMessage.user.payGrade !== null && _decodeMessage.user.payGrade !== undefined) {
        _newMessage.user.userPrivilegeLevel = _decodeMessage.user.payGrade.level;
    }
    if (_decodeMessage.user.badgeList !== undefined && _decodeMessage.user.badgeList !== null) {
        // processDebugUserBadges(_decodeMessage.user.badgeList, _newMessage);
        // 添加徽章数组
        _newMessage.user.combineBadges = [];
        _decodeMessage.user.badgeList.forEach(function (badge) {
            if (badge.displayType === pb_webcast.webcast.BadgeDisplayType.BADGE_DISPLAY_TYPE_COMBINE) {
                if (badge.sceneType === pb_webcast.webcast.BadgeSceneType.BadgeSceneType_UserGrade) {
                    _newMessage.user.userPrivilegeLevel = Number(badge.privilegeLogExtra.level);
                    // self_debug(badge.combine.str + ', color:' + badge.combine.background.backgroundColorCode + ', icon:' + badge.combine?.icon?.urlList[0]);
                } else if (badge.sceneType === pb_webcast.webcast.BadgeSceneType.BadgeSceneType_Fans) {
                    _newMessage.user.fansclubLevel = Number(badge.privilegeLogExtra.level);
                }
                var combineBadge = {
                    sceneType: badge.sceneType,
                    str: badge.combine.str,
                    backgroundColorCode: badge.combine && badge.combine.background ? badge.combine.background.backgroundColorCode : null,
                    iconUrl: badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : null,
                    text: badge.combine && badge.combine.text ? badge.combine.text.defaultPattern : null,
                    level: badge.privilegeLogExtra ? badge.privilegeLogExtra.level : null
                };
                _newMessage.user.combineBadges.push(combineBadge);
            }
        });
    }
}
