{"name": "tiktok-plugin", "version": "1.0.0", "scripts": {"hunxiao-proto": "rollup -c rollup-hunxiao-proto.config.mjs"}, "dependencies": {"@rollup/plugin-commonjs": "^28.0.2", "body-parser": "^2.2.0", "express": "^5.1.0", "jsdom": "^26.0.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "sqlite3": "^5.1.7"}, "devDependencies": {"long": "^5.2.4", "protobufjs": "^7.4.0", "ts-loader": "^9.5.2", "typescript": "^5.7.3", "webpack": "^5.97.1", "webpack-cli": "^6.0.1"}}