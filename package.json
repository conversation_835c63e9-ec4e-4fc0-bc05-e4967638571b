{"name": "tiktok-live-monitor", "version": "2.0.0", "description": "TikTok直播间数据监听系统，支持WebSocket通信和多房间管理", "main": "start-server.js", "scripts": {"start": "node start-server.js", "start:server": "node start-server.js", "start:client": "node start-client.js", "dev:server": "NODE_ENV=development node start-server.js", "dev:client": "NODE_ENV=development node start-client.js", "test": "node test-system.js", "test:system": "node test-system.js", "test:puppeteer": "node test-puppeteer.js", "test:puppeteer:monitor": "node test-puppeteer.js monitor", "test:config": "node test-config-persistence.js", "test:config:persistence": "node test-config-persistence.js persistence", "demo:config": "node examples/config-persistence-demo.js", "demo:config:quick": "node examples/config-persistence-demo.js quick", "hunxiao-proto": "rollup -c rollup-hunxiao-proto.config.mjs"}, "keywords": ["tiktok", "live", "monitor", "websocket", "electron", "streaming"], "author": "Your Name", "license": "MIT", "dependencies": {"@rollup/plugin-commonjs": "^28.0.2", "body-parser": "^2.2.0", "express": "^5.1.0", "jsdom": "^26.0.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "sqlite3": "^5.1.7", "ws": "^8.18.0"}, "devDependencies": {"long": "^5.2.4", "protobufjs": "^7.4.0", "ts-loader": "^9.5.2", "typescript": "^5.7.3", "webpack": "^5.97.1", "webpack-cli": "^6.0.1"}, "engines": {"node": ">=16.0.0"}}