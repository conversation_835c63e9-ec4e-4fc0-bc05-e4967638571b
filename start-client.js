#!/usr/bin/env node

/**
 * 客户端启动脚本
 * 启动Electron客户端应用程序
 */

const { loadConfig } = require('./config/config-loader');
const { ElectronClientApp } = require('./client/client-app');

async function startClient() {
  try {
    console.log('Starting TikTok Live Monitor Client...');
    
    // 加载配置
    const environment = process.env.NODE_ENV || 'default';
    const config = loadConfig(environment);
    
    console.log('Configuration loaded:', {
      environment,
      serverUrl: config.client.serverUrl,
      maxRooms: config.client.maxRooms,
      proxyServer: config.client.proxyServer,
    });
    
    // 创建客户端应用
    const clientApp = new ElectronClientApp({
      serverUrl: config.client.serverUrl,
      clientId: config.client.clientId,
      maxRooms: config.client.maxRooms,
      proxyServer: config.client.proxyServer,
      reconnectInterval: config.client.reconnectInterval,
      maxReconnectAttempts: config.client.maxReconnectAttempts,
      heartbeatInterval: config.client.heartbeatInterval,
      windowOptions: config.client.windowOptions,
    });
    
    // 设置优雅关闭
    const gracefulShutdown = async (signal) => {
      console.log(`\nReceived ${signal}, shutting down gracefully...`);
      
      try {
        await clientApp.stop();
        console.log('Client stopped successfully');
        process.exit(0);
      } catch (error) {
        console.error('Error during shutdown:', error);
        process.exit(1);
      }
    };
    
    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
    // 监听未捕获的异常
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      gracefulShutdown('uncaughtException');
    });
    
    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      gracefulShutdown('unhandledRejection');
    });
    
    // 启动客户端
    await clientApp.start();
    
    console.log('\n=== TikTok Live Monitor Client Started ===');
    console.log(`Connected to: ${config.client.serverUrl}`);
    console.log(`Max Rooms: ${config.client.maxRooms}`);
    console.log(`Client ID: ${clientApp.wsClient.clientId}`);
    console.log('Waiting for room monitoring requests...');
    console.log('Press Ctrl+C to stop the client');
    console.log('==========================================\n');
    
    // 设置状态报告
    setInterval(() => {
      const status = clientApp.getStatus();
      console.log(`[STATUS] Running: ${status.isRunning}, Connected: ${status.connection.isConnected}, Rooms: ${status.rooms.totalRooms}`);
    }, 60000); // 每分钟报告一次状态
    
  } catch (error) {
    console.error('Failed to start client:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本，启动客户端
if (require.main === module) {
  startClient();
}

module.exports = { startClient };
