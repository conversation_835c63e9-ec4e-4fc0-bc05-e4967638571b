/**
 * 数据库管理器
 * 负责SQLite数据库的初始化和数据操作
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class DatabaseManager {
  constructor(options = {}) {
    this.dbPath = options.dbPath || path.join(__dirname, '../data/monitor.db');
    this.db = null;
    
    // 确保数据目录存在
    const dataDir = path.dirname(this.dbPath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
  }

  /**
   * 初始化数据库
   */
  async initialize() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('Failed to open database:', err);
          reject(err);
        } else {
          console.log('Database connected:', this.dbPath);
          this.createTables().then(resolve).catch(reject);
        }
      });
    });
  }

  /**
   * 创建数据表
   */
  async createTables() {
    const tables = [
      // 客户端表
      `CREATE TABLE IF NOT EXISTS clients (
        id TEXT PRIMARY KEY,
        info TEXT,
        last_connected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // 房间配置表
      `CREATE TABLE IF NOT EXISTS room_configs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        client_id TEXT NOT NULL,
        room_id TEXT NOT NULL,
        room_url TEXT NOT NULL,
        config TEXT,
        status TEXT DEFAULT 'inactive',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(client_id, room_id),
        FOREIGN KEY(client_id) REFERENCES clients(id) ON DELETE CASCADE
      )`,
      
      // 事件日志表（可选，用于统计）
      `CREATE TABLE IF NOT EXISTS event_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        client_id TEXT NOT NULL,
        room_id TEXT NOT NULL,
        event_type TEXT NOT NULL,
        event_data TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY(client_id) REFERENCES clients(id) ON DELETE CASCADE
      )`,
      
      // 索引
      `CREATE INDEX IF NOT EXISTS idx_clients_last_connected 
       ON clients(last_connected_at)`,
      
      `CREATE INDEX IF NOT EXISTS idx_room_configs_client_status 
       ON room_configs(client_id, status)`,
      
      `CREATE INDEX IF NOT EXISTS idx_event_logs_client_room_time 
       ON event_logs(client_id, room_id, created_at)`
    ];

    for (const sql of tables) {
      await this.run(sql);
    }
    
    console.log('Database tables created/verified');
  }

  /**
   * 执行SQL语句
   */
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ lastID: this.lastID, changes: this.changes });
        }
      });
    });
  }

  /**
   * 查询单条记录
   */
  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  /**
   * 查询多条记录
   */
  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  /**
   * 保存或更新客户端信息
   */
  async saveClient(clientId, clientInfo) {
    const sql = `
      INSERT OR REPLACE INTO clients (id, info, last_connected_at, updated_at)
      VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `;
    
    return await this.run(sql, [
      clientId,
      JSON.stringify(clientInfo)
    ]);
  }

  /**
   * 获取客户端信息
   */
  async getClient(clientId) {
    const sql = 'SELECT * FROM clients WHERE id = ?';
    const row = await this.get(sql, [clientId]);
    
    if (row) {
      return {
        ...row,
        info: JSON.parse(row.info || '{}')
      };
    }
    
    return null;
  }

  /**
   * 保存房间配置
   */
  async saveRoomConfig(clientId, roomId, roomUrl, config = {}, status = 'active') {
    const sql = `
      INSERT OR REPLACE INTO room_configs 
      (client_id, room_id, room_url, config, status, updated_at)
      VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `;
    
    return await this.run(sql, [
      clientId,
      roomId,
      roomUrl,
      JSON.stringify(config),
      status
    ]);
  }

  /**
   * 获取客户端的房间配置
   */
  async getClientRoomConfigs(clientId, status = null) {
    let sql = 'SELECT * FROM room_configs WHERE client_id = ?';
    const params = [clientId];
    
    if (status) {
      sql += ' AND status = ?';
      params.push(status);
    }
    
    sql += ' ORDER BY updated_at DESC';
    
    const rows = await this.all(sql, params);
    
    return rows.map(row => ({
      ...row,
      config: JSON.parse(row.config || '{}')
    }));
  }

  /**
   * 更新房间状态
   */
  async updateRoomStatus(clientId, roomId, status) {
    const sql = `
      UPDATE room_configs 
      SET status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE client_id = ? AND room_id = ?
    `;
    
    return await this.run(sql, [status, clientId, roomId]);
  }

  /**
   * 删除房间配置
   */
  async deleteRoomConfig(clientId, roomId) {
    const sql = 'DELETE FROM room_configs WHERE client_id = ? AND room_id = ?';
    return await this.run(sql, [clientId, roomId]);
  }

  /**
   * 记录事件日志
   */
  async logEvent(clientId, roomId, eventType, eventData) {
    const sql = `
      INSERT INTO event_logs (client_id, room_id, event_type, event_data)
      VALUES (?, ?, ?, ?)
    `;
    
    return await this.run(sql, [
      clientId,
      roomId,
      eventType,
      JSON.stringify(eventData)
    ]);
  }

  /**
   * 获取事件统计
   */
  async getEventStats(clientId, roomId = null, hours = 24) {
    let sql = `
      SELECT 
        event_type,
        COUNT(*) as count,
        MIN(created_at) as first_event,
        MAX(created_at) as last_event
      FROM event_logs 
      WHERE client_id = ? 
        AND created_at > datetime('now', '-${hours} hours')
    `;
    
    const params = [clientId];
    
    if (roomId) {
      sql += ' AND room_id = ?';
      params.push(roomId);
    }
    
    sql += ' GROUP BY event_type ORDER BY count DESC';
    
    return await this.all(sql, params);
  }

  /**
   * 清理旧数据
   */
  async cleanupOldData(days = 30) {
    const sqls = [
      `DELETE FROM event_logs WHERE created_at < datetime('now', '-${days} days')`,
      `DELETE FROM clients WHERE last_connected_at < datetime('now', '-${days} days')`,
    ];
    
    let totalDeleted = 0;
    
    for (const sql of sqls) {
      const result = await this.run(sql);
      totalDeleted += result.changes;
    }
    
    console.log(`Cleaned up ${totalDeleted} old records`);
    return totalDeleted;
  }

  /**
   * 获取数据库统计信息
   */
  async getStats() {
    const stats = {};
    
    const tables = ['clients', 'room_configs', 'event_logs'];
    
    for (const table of tables) {
      const result = await this.get(`SELECT COUNT(*) as count FROM ${table}`);
      stats[table] = result.count;
    }
    
    return stats;
  }

  /**
   * 关闭数据库连接
   */
  async close() {
    return new Promise((resolve) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            console.error('Error closing database:', err);
          } else {
            console.log('Database connection closed');
          }
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

module.exports = DatabaseManager;
