/**
 * WebSocket 服务端
 * 负责管理客户端连接、房间管理和事件分发
 */

const WebSocket = require('ws');
const EventEmitter = require('events');
const DatabaseManager = require('./database-manager');
const {
  MessageType,
  ServerMessage,
  ClientMessage,
  BaseMessage,
  RoomStatus
} = require('../protocol/websocket-protocol');

class WebSocketServer extends EventEmitter {
  constructor(options = {}) {
    super();

    this.port = options.port || 8080;
    this.host = options.host || 'localhost';
    this.wss = null;

    // 数据库管理器
    this.db = new DatabaseManager(options.database);

    // 客户端连接管理
    this.clients = new Map(); // clientId -> { ws, info, rooms }

    // 房间管理
    this.rooms = new Map(); // roomId -> { roomUrl, config, status, clientId }

    // 消息处理器
    this.messageHandlers = new Map();
    this.setupMessageHandlers();

    // 心跳配置
    this.heartbeatInterval = options.heartbeatInterval || 30000;
    this.heartbeatTimer = null;

    // 配置管理
    this.enableConfigPersistence = options.enableConfigPersistence !== false;
  }

  /**
   * 启动服务器
   */
  async start() {
    try {
      // 初始化数据库
      if (this.enableConfigPersistence) {
        await this.db.initialize();
        console.log('Database initialized');
      }

      return new Promise((resolve, reject) => {
        try {
          this.wss = new WebSocket.Server({
            port: this.port,
            host: this.host,
          });

          this.wss.on('connection', (ws, req) => {
            this.handleConnection(ws, req);
          });

          this.wss.on('error', (error) => {
            console.error('WebSocket Server Error:', error);
            this.emit('error', error);
          });

          this.wss.on('listening', () => {
            console.log(`WebSocket Server listening on ${this.host}:${this.port}`);
            this.startHeartbeat();
            resolve();
          });

        } catch (error) {
          reject(error);
        }
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * 停止服务器
   */
  async stop() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }

    if (this.wss) {
      this.wss.close();
      this.wss = null;
    }

    // 关闭数据库连接
    if (this.db) {
      await this.db.close();
    }

    this.clients.clear();
    this.rooms.clear();
  }

  /**
   * 处理新连接
   */
  handleConnection(ws, req) {
    const clientId = this.generateClientId();
    console.log(`Client connected: ${clientId}`);

    const clientInfo = {
      ws,
      id: clientId,
      ip: req.socket.remoteAddress,
      userAgent: req.headers['user-agent'],
      connectedAt: Date.now(),
      rooms: new Set(),
      lastHeartbeat: Date.now(),
    };

    this.clients.set(clientId, clientInfo);

    // 设置消息处理
    ws.on('message', (data) => {
      this.handleMessage(clientId, data);
    });

    // 设置连接关闭处理
    ws.on('close', () => {
      this.handleDisconnection(clientId);
    });

    // 设置错误处理
    ws.on('error', (error) => {
      console.error(`Client ${clientId} error:`, error);
      this.handleDisconnection(clientId);
    });

    this.emit('clientConnected', clientInfo);
  }

  /**
   * 处理客户端断开连接
   */
  handleDisconnection(clientId) {
    const client = this.clients.get(clientId);
    if (!client) return;

    console.log(`Client disconnected: ${clientId}`);

    // 清理该客户端的房间
    for (const roomId of client.rooms) {
      this.rooms.delete(roomId);
    }

    this.clients.delete(clientId);
    this.emit('clientDisconnected', client);
  }

  /**
   * 处理消息
   */
  handleMessage(clientId, data) {
    try {
      const message = BaseMessage.fromJSON(data);
      const handler = this.messageHandlers.get(message.type);
      
      if (handler) {
        handler.call(this, clientId, message);
      } else {
        console.warn(`Unknown message type: ${message.type}`);
      }
    } catch (error) {
      console.error('Message handling error:', error);
      this.sendError(clientId, error);
    }
  }

  /**
   * 设置消息处理器
   */
  setupMessageHandlers() {
    this.messageHandlers.set(MessageType.CLIENT_READY, this.handleClientReady);
    this.messageHandlers.set(MessageType.ROOM_STATUS, this.handleRoomStatus);
    this.messageHandlers.set(MessageType.LIVE_EVENT, this.handleLiveEvent);
    this.messageHandlers.set(MessageType.HEARTBEAT, this.handleHeartbeat);
    this.messageHandlers.set(MessageType.ERROR, this.handleClientError);
    this.messageHandlers.set(MessageType.CONFIG_SAVE, this.handleConfigSave);
  }

  /**
   * 处理客户端就绪消息
   */
  async handleClientReady(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client) return;

    client.info = message.data;
    console.log(`Client ${clientId} ready:`, message.data);

    // 保存客户端信息到数据库
    if (this.enableConfigPersistence) {
      try {
        await this.db.saveClient(clientId, message.data);

        // 恢复客户端的房间配置
        await this.restoreClientConfig(clientId);
      } catch (error) {
        console.error(`Failed to save client ${clientId}:`, error);
      }
    }

    this.emit('clientReady', client);
  }

  /**
   * 处理房间状态更新
   */
  handleRoomStatus(clientId, message) {
    const { roomId, status, details } = message.data;
    const room = this.rooms.get(roomId);
    
    if (room) {
      room.status = status;
      room.details = { ...room.details, ...details };
      room.lastUpdate = Date.now();
    }

    console.log(`Room ${roomId} status: ${status}`, details);
    this.emit('roomStatusUpdate', { roomId, status, details });
  }

  /**
   * 处理直播间事件
   */
  handleLiveEvent(clientId, message) {
    const { roomId, eventType, eventData } = message.data;
    
    console.log(`Live event from room ${roomId}: ${eventType}`, eventData);
    this.emit('liveEvent', { roomId, eventType, eventData, clientId });
  }

  /**
   * 处理心跳消息
   */
  handleHeartbeat(clientId, message) {
    const client = this.clients.get(clientId);
    if (client) {
      client.lastHeartbeat = Date.now();
    }
  }

  /**
   * 处理客户端错误
   */
  handleClientError(clientId, message) {
    console.error(`Client ${clientId} error:`, message.data);
    this.emit('clientError', { clientId, error: message.data });
  }

  /**
   * 处理配置保存
   */
  async handleConfigSave(clientId, message) {
    if (!this.enableConfigPersistence) return;

    try {
      const { roomConfigs } = message.data;
      console.log(`Saving config for client ${clientId}:`, roomConfigs);

      // 保存每个房间配置
      for (const roomConfig of roomConfigs) {
        await this.db.saveRoomConfig(
          clientId,
          roomConfig.roomId,
          roomConfig.roomUrl,
          roomConfig.config,
          roomConfig.status || 'active'
        );
      }

      console.log(`Config saved for client ${clientId}`);

    } catch (error) {
      console.error(`Failed to save config for client ${clientId}:`, error);
      this.sendError(clientId, error);
    }
  }

  /**
   * 发送消息到客户端
   */
  sendMessage(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client || client.ws.readyState !== WebSocket.OPEN) {
      return false;
    }

    try {
      client.ws.send(JSON.stringify(message.toJSON()));
      return true;
    } catch (error) {
      console.error(`Failed to send message to client ${clientId}:`, error);
      return false;
    }
  }

  /**
   * 广播消息到所有客户端
   */
  broadcast(message) {
    let sent = 0;
    for (const [clientId] of this.clients) {
      if (this.sendMessage(clientId, message)) {
        sent++;
      }
    }
    return sent;
  }

  /**
   * 发送错误消息
   */
  sendError(clientId, error) {
    const errorMessage = new ClientMessage(MessageType.ERROR, {
      error: {
        message: error.message || error,
        code: error.code || 'UNKNOWN_ERROR',
        stack: error.stack,
      },
    });
    this.sendMessage(clientId, errorMessage);
  }

  /**
   * 恢复客户端配置
   */
  async restoreClientConfig(clientId) {
    if (!this.enableConfigPersistence) return;

    try {
      // 获取客户端的活跃房间配置
      const roomConfigs = await this.db.getClientRoomConfigs(clientId, 'active');

      if (roomConfigs.length > 0) {
        console.log(`Restoring ${roomConfigs.length} room configs for client ${clientId}`);

        // 发送恢复配置消息
        const message = ServerMessage.restoreConfig(roomConfigs);
        this.sendMessage(clientId, message);

        // 重新添加房间到内存中
        const client = this.clients.get(clientId);
        if (client) {
          for (const roomConfig of roomConfigs) {
            const room = {
              roomId: roomConfig.room_id,
              roomUrl: roomConfig.room_url,
              config: roomConfig.config,
              status: RoomStatus.CONNECTING,
              clientId: clientId,
              createdAt: Date.now(),
              details: {},
            };

            this.rooms.set(roomConfig.room_id, room);
            client.rooms.add(roomConfig.room_id);
          }
        }

        this.emit('configRestored', { clientId, roomConfigs });
      } else {
        console.log(`No saved config found for client ${clientId}`);
      }

    } catch (error) {
      console.error(`Failed to restore config for client ${clientId}:`, error);
    }
  }

  /**
   * 添加房间监听
   */
  async addRoom(roomId, roomUrl, config = {}) {
    // 找到可用的客户端
    const availableClient = this.findAvailableClient();
    if (!availableClient) {
      throw new Error('No available client to handle room monitoring');
    }

    const room = {
      roomId,
      roomUrl,
      config,
      status: RoomStatus.CONNECTING,
      clientId: availableClient.id,
      createdAt: Date.now(),
      details: {},
    };

    this.rooms.set(roomId, room);
    availableClient.rooms.add(roomId);

    // 保存房间配置到数据库
    if (this.enableConfigPersistence) {
      try {
        await this.db.saveRoomConfig(availableClient.id, roomId, roomUrl, config, 'active');
      } catch (error) {
        console.error(`Failed to save room config:`, error);
      }
    }

    // 发送添加房间消息到客户端
    const message = ServerMessage.addRoom(roomId, roomUrl, config);
    this.sendMessage(availableClient.id, message);

    return room;
  }

  /**
   * 移除房间监听
   */
  async removeRoom(roomId) {
    const room = this.rooms.get(roomId);
    if (!room) return false;

    const client = this.clients.get(room.clientId);
    if (client) {
      client.rooms.delete(roomId);
      const message = ServerMessage.removeRoom(roomId);
      this.sendMessage(client.id, message);

      // 更新数据库中的房间状态
      if (this.enableConfigPersistence) {
        try {
          await this.db.updateRoomStatus(client.id, roomId, 'inactive');
        } catch (error) {
          console.error(`Failed to update room status:`, error);
        }
      }
    }

    this.rooms.delete(roomId);
    return true;
  }

  /**
   * 获取房间列表
   */
  getRooms() {
    return Array.from(this.rooms.values());
  }

  /**
   * 获取客户端列表
   */
  getClients() {
    return Array.from(this.clients.values()).map(client => ({
      id: client.id,
      ip: client.ip,
      userAgent: client.userAgent,
      connectedAt: client.connectedAt,
      rooms: Array.from(client.rooms),
      lastHeartbeat: client.lastHeartbeat,
    }));
  }

  /**
   * 查找可用的客户端
   */
  findAvailableClient() {
    for (const client of this.clients.values()) {
      if (client.ws.readyState === WebSocket.OPEN) {
        return client;
      }
    }
    return null;
  }

  /**
   * 启动心跳检测
   */
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      const now = Date.now();
      const timeout = this.heartbeatInterval * 2;

      for (const [clientId, client] of this.clients) {
        if (now - client.lastHeartbeat > timeout) {
          console.log(`Client ${clientId} heartbeat timeout, disconnecting`);
          client.ws.terminate();
          this.handleDisconnection(clientId);
        }
      }
    }, this.heartbeatInterval);
  }

  /**
   * 生成客户端ID
   */
  generateClientId() {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

module.exports = WebSocketServer;
