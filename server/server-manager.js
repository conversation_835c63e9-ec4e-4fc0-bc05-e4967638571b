/**
 * 服务端管理器
 * 提供HTTP API和WebSocket服务的统一管理
 */

const express = require('express');
const WebSocketServer = require('./websocket-server');
const { RoomStatus } = require('../protocol/websocket-protocol');

class ServerManager {
  constructor(options = {}) {
    this.httpPort = options.httpPort || 3000;
    this.wsPort = options.wsPort || 8080;
    this.host = options.host || 'localhost';
    
    // 创建WebSocket服务器
    this.wsServer = new WebSocketServer({
      port: this.wsPort,
      host: this.host,
    });
    
    // 创建HTTP服务器
    this.app = express();
    this.httpServer = null;
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocketEvents();
  }

  /**
   * 设置中间件
   */
  setupMiddleware() {
    this.app.use(express.json());
    this.app.use(express.static('public'));
    
    // CORS支持
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    });
  }

  /**
   * 设置HTTP路由
   */
  setupRoutes() {
    // 获取服务器状态
    this.app.get('/api/status', (req, res) => {
      res.json({
        success: true,
        data: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          clients: this.wsServer.getClients().length,
          rooms: this.wsServer.getRooms().length,
        },
      });
    });

    // 获取客户端列表
    this.app.get('/api/clients', (req, res) => {
      res.json({
        success: true,
        data: this.wsServer.getClients(),
      });
    });

    // 获取房间列表
    this.app.get('/api/rooms', (req, res) => {
      res.json({
        success: true,
        data: this.wsServer.getRooms(),
      });
    });

    // 添加房间监听
    this.app.post('/api/rooms', (req, res) => {
      try {
        const { roomId, roomUrl, config } = req.body;
        
        if (!roomId || !roomUrl) {
          return res.status(400).json({
            success: false,
            error: 'roomId and roomUrl are required',
          });
        }

        const room = this.wsServer.addRoom(roomId, roomUrl, config);
        
        res.json({
          success: true,
          data: room,
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error.message,
        });
      }
    });

    // 删除房间监听
    this.app.delete('/api/rooms/:roomId', (req, res) => {
      try {
        const { roomId } = req.params;
        const success = this.wsServer.removeRoom(roomId);
        
        if (success) {
          res.json({
            success: true,
            message: `Room ${roomId} removed successfully`,
          });
        } else {
          res.status(404).json({
            success: false,
            error: `Room ${roomId} not found`,
          });
        }
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error.message,
        });
      }
    });

    // 获取特定房间信息
    this.app.get('/api/rooms/:roomId', (req, res) => {
      const { roomId } = req.params;
      const rooms = this.wsServer.getRooms();
      const room = rooms.find(r => r.roomId === roomId);
      
      if (room) {
        res.json({
          success: true,
          data: room,
        });
      } else {
        res.status(404).json({
          success: false,
          error: `Room ${roomId} not found`,
        });
      }
    });

    // 健康检查
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
      });
    });

    // 错误处理中间件
    this.app.use((error, req, res, next) => {
      console.error('HTTP Error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    });
  }

  /**
   * 设置WebSocket事件处理
   */
  setupWebSocketEvents() {
    this.wsServer.on('clientConnected', (client) => {
      console.log(`[WS] Client connected: ${client.id} from ${client.ip}`);
    });

    this.wsServer.on('clientDisconnected', (client) => {
      console.log(`[WS] Client disconnected: ${client.id}`);
    });

    this.wsServer.on('clientReady', (client) => {
      console.log(`[WS] Client ready: ${client.id}`, client.info);
    });

    this.wsServer.on('roomStatusUpdate', ({ roomId, status, details }) => {
      console.log(`[WS] Room ${roomId} status: ${status}`, details);
    });

    this.wsServer.on('liveEvent', ({ roomId, eventType, eventData, clientId }) => {
      console.log(`[WS] Live event from ${roomId}: ${eventType}`, eventData);
      
      // 这里可以添加事件处理逻辑，比如存储到数据库、转发到其他系统等
      this.handleLiveEvent(roomId, eventType, eventData, clientId);
    });

    this.wsServer.on('clientError', ({ clientId, error }) => {
      console.error(`[WS] Client ${clientId} error:`, error);
    });

    this.wsServer.on('error', (error) => {
      console.error('[WS] Server error:', error);
    });
  }

  /**
   * 处理直播间事件
   */
  handleLiveEvent(roomId, eventType, eventData, clientId) {
    // 可以在这里添加自定义的事件处理逻辑
    // 例如：存储到数据库、发送通知、触发其他业务逻辑等
    
    const event = {
      roomId,
      eventType,
      eventData,
      clientId,
      timestamp: Date.now(),
    };

    // 示例：打印重要事件
    if (['gift', 'follow', 'live_end'].includes(eventType)) {
      console.log(`[IMPORTANT] ${eventType.toUpperCase()} event in room ${roomId}:`, eventData);
    }

    // 可以添加更多处理逻辑
    // - 存储到数据库
    // - 发送到消息队列
    // - 触发Webhook
    // - 实时推送到前端等
  }

  /**
   * 启动服务器
   */
  async start() {
    try {
      // 启动WebSocket服务器
      await this.wsServer.start();
      console.log(`WebSocket Server started on ws://${this.host}:${this.wsPort}`);

      // 启动HTTP服务器
      await new Promise((resolve, reject) => {
        this.httpServer = this.app.listen(this.httpPort, this.host, (error) => {
          if (error) {
            reject(error);
          } else {
            console.log(`HTTP Server started on http://${this.host}:${this.httpPort}`);
            resolve();
          }
        });
      });

      console.log('Server Manager started successfully');
      console.log(`API Documentation: http://${this.host}:${this.httpPort}/api/status`);
      
    } catch (error) {
      console.error('Failed to start server:', error);
      throw error;
    }
  }

  /**
   * 停止服务器
   */
  async stop() {
    console.log('Stopping servers...');
    
    if (this.httpServer) {
      this.httpServer.close();
      this.httpServer = null;
    }
    
    await this.wsServer.stop();
    console.log('Servers stopped');
  }
}

module.exports = ServerManager;
